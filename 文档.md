# wolf-static-cpnt 使用文档

## 概述

wolf-static-cpnt是一个基于 React 的组件库，主要用于数据分析和流程管理。所有组件都基于 Filter 架构设计，提供统一的数据过滤和处理能力。本文档基于最新的 TypeScript 类型定义文件编写。

## 安装和引入

```bash
pnpm install wolf-static-cpnt
```

```typescript
import { 
  Filter, 
  Label, 
  EventFilter, 
  ActionCollective, 
  Complex,
  Customize,
  FlowEditor,
  SelectTime,
  SelectTimeV2,
  GrabbingBox 
} from 'wolf-static-cpnt';

// 引入类型定义
import type { 
  FilterProps, 
  FilterRef, 
  FilterValue,
  LabelProps,
  ActionCollectiveProps,
  DataProvider 
} from 'wolf-static-cpnt';
```

---

## 核心接口定义

### 数据提供器接口 (DataProvider)

所有过滤类组件的基础接口：

```typescript
interface DataProvider {
  /**
   * 获取属性列表
   * @param name 属性名称（可选）
   * @returns 属性列表
   */
  getPropertyList: (name?: string) => Property[] | Promise<Property[]>;

  /**
   * 获取属性枚举值列表
   * @param _tableId 表ID
   * @param _schemaId 模式ID
   * @returns 枚举值列表
   */
  getPropertyEnumList?: (_tableId: number, _schemaId: number) => any[] | Promise<any[]>;
}
```

### 属性接口 (Property)

```typescript
interface Property {
  tableId: number;
  schemaId: number;
  fieldName: string;
  field: string;
  fieldType: 'STRING' | 'INT' | 'FLOAT' | 'DATE' | 'DATETIME' | 'BOOLEAN' | 'HIVE_DATE' | 'HIVE_TIMESTAMP';
  level1?: string;
  level2?: string;
  isEnum?: boolean;
}
```

### 过滤器值结构 (FilterValue)

```typescript
interface FilterValue {
  connector: 'AND' | 'OR';
  filters: FilterGroup[];
}

interface FilterGroup {
  connector: 'AND' | 'OR';
  filters: FilterCondition[];
}

interface FilterCondition {
  tableId: number;
  schemaId: number;
  fieldName: string;
  field: string;
  fieldType: string;
  operator: 'EQ' | 'NOT_EQ' | 'GT' | 'GTE' | 'LT' | 'LTE' | 'BETWEEN' | 'IN' | 'NOT_IN' | 'LIKE' | 'NOT_LIKE' | 'IS_NULL' | 'IS_NOT_NULL';
  value?: any;
  isEnum?: boolean;
}
```

---

## 组件详细说明

### 1. Filter 组件

**描述：** 基础过滤器组件，是其他过滤组件的基础。

#### Props (FilterProps)

```typescript
interface FilterProps {
  /** 过滤器的值，包含filters数组 */
  value?: FilterValue;
  
  /** 数据提供器，需包含getPropertyList方法 */
  dataProvider: DataProvider;
  
  /** 值改变时的回调函数 */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;
  
  /** 组件模式，edit可编辑，detail只读 @default 'edit' */
  mode?: 'edit' | 'detail';
  
  /** 是否隐藏添加按钮 @default false */
  hideAdd?: boolean;
  
  /** 是否隐藏初始过滤条件 @default false */
  hideInit?: boolean;
  
  /** 自定义CSS类名 */
  className?: string;
  
  /** 添加按钮的文本 */
  addButtonText?: string;
  
  /** 是否为用户分群模式 */
  isUserGroup?: boolean;
}
```

#### Ref 方法 (FilterRef)

```typescript
interface FilterRef {
  /** 校验过滤器是否有效 */
  isValid: (flag?: boolean) => boolean;
  
  /** 添加新的过滤组 */
  addFilterGroup: () => FilterValue;
  
  /** 获取当前过滤器的值 */
  getValue: () => FilterValue;
  
  /** 获取过滤条件总数 */
  getFilterCount: () => number;
}
```

#### 使用示例

```typescript
import React, { useRef } from 'react';
import { Filter } from 'wolf-static-cpnt';
import type { FilterProps, FilterRef, FilterValue, DataProvider } from 'wolf-static-cpnt';

const dataProvider: DataProvider = {
  getPropertyList: async (name) => {
    // 返回属性列表数据
    return [
      {
        tableId: 1,
        schemaId: 1,
        fieldName: 'name',
        field: 'user_name',
        fieldType: 'STRING',
        level1: 'user',
        level2: 'profile'
      }
    ];
  }
};

const MyComponent: React.FC = () => {
  const filterRef = useRef<FilterRef>(null);
  const [filterValue, setFilterValue] = useState<FilterValue>();

  return (
    <Filter
      ref={filterRef}
      dataProvider={dataProvider}
      value={filterValue}
      onChange={(value, innerValue) => {
        setFilterValue(value);
        console.log('Filter changed:', value);
      }}
      mode="edit"
      className="custom-filter"
    />
  );
};
```

---

### 2. Label 组件

**描述：** 标签过滤器组件，专门用于标签数据的过滤。

#### Props (LabelProps)

```typescript
interface LabelProps {
  /** 标签过滤器的值 */
  value?: FilterValue;
  
  /** 数据提供器 */
  dataProvider: DataProvider;
  
  /** 值改变时的回调函数 */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;
  
  /** 组件模式 @default 'edit' */
  mode?: 'edit' | 'detail';
  
  /** 是否显示初始行 @default true */
  showInitLine?: boolean;
  
  /** 是否选中状态 @default false */
  checked?: boolean;
  
  /** 活动信息 @default {} */
  campaignInfo?: CampaignInfo;
  
  /** 是否为用户分群模式 */
  isUserGroup?: boolean;
  
  /** 是否为活动V2版本 */
  isCampaignV2?: boolean;
}
```

#### Ref 方法 (LabelRef)

```typescript
interface LabelRef {
  /** 校验标签过滤器是否有效 */
  isValid: (flag?: boolean) => boolean;
  
  /** 获取过滤条件总数 */
  getFilterCount: () => number;
}
```

---

### 3. EventFilter 组件

**描述：** 事件过滤器组件，专门用于事件数据的过滤。

#### Props (EventFilterProps)

```typescript
interface EventFilterProps {
  /** 事件过滤器的值 */
  value?: FilterValue;
  
  /** 事件数据提供器 */
  dataProvider: EventDataProvider;
  
  /** 值改变时的回调函数 */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;
  
  /** 组件模式 @default 'edit' */
  mode?: 'edit' | 'detail';
  
  /** 是否显示初始行 @default true */
  showInitLine?: boolean;
  
  /** 是否为行为集合模式 */
  isActionCollection?: boolean;
}
```

#### 事件数据提供器 (EventDataProvider)

```typescript
interface EventDataProvider extends DataProvider {
  /** 获取事件列表 */
  getEventList?: () => Event[] | Promise<Event[]>;
  
  /** 获取事件计数日志 */
  getEventCountLogsByProjectId?: () => any[] | Promise<any[]>;
}
```

---

### 4. ActionCollective 组件

**描述：** 行为集合组件，组合多种过滤器（事件、用户属性、标签、分群）。

#### Props (ActionCollectiveProps)

```typescript
interface ActionCollectiveProps {
  /** 行为集合的值 */
  value?: ActionCollectiveValue;
  
  /** 行为集合数据提供器 */
  dataProvider: ActionCollectiveDataProvider;
  
  /** 值改变时的回调函数 */
  onChange?: (value: ActionCollectiveValue) => void;
  
  /** 组件模式 @default 'edit' */
  mode?: 'edit' | 'detail';
  
  /** 是否显示初始行 */
  showInitLine?: boolean;
  
  /** 是否为行为集合分群模式 */
  isActionCollection?: boolean;
  
  /** 是否为用户分群模式 */
  isUserGroup?: boolean;
}
```

#### 行为集合值结构 (ActionCollectiveValue)

```typescript
interface ActionCollectiveValue {
  connector: 'AND' | 'OR';
  filters: ActionCollectiveFilterItem[];
}

interface ActionCollectiveFilterItem {
  connector: 'AND' | 'OR';
  eventGroup?: FilterValue;
  userProperty?: FilterValue;
  userLabel?: FilterValue;
  segment?: FilterValue;
}
```

#### 行为集合数据提供器 (ActionCollectiveDataProvider)

```typescript
interface ActionCollectiveDataProvider extends EventDataProvider {
  /** 获取标签列表 */
  getLabelList?: () => Label[] | Promise<Label[]>;
  
  /** 获取细分列表 */
  getSegmentList?: () => any[] | Promise<any[]>;
}
```

---

### 5. Complex 组件

**描述：** 复合过滤器组件，支持分群选择和组合。

#### Props (ComplexProps)

```typescript
interface ComplexProps {
  /** 过滤器值 */
  value?: ComplexValue;
  
  /** 数据提供器 */
  dataProvider: ComplexDataProvider;
  
  /** 值变化回调 */
  onChange?: (value: ComplexValue, rawValue?: any) => void;
  
  /** 组件模式 */
  mode?: 'edit' | 'detail';
  
  /** 分群选择列表 */
  selectList?: SegmentItem[];
  
  /** 是否为用户分群 */
  isUserGroup?: boolean;
  
  /** 样式类名 */
  className?: string;
}
```

#### 复合过滤器值结构 (ComplexValue)

```typescript
interface ComplexValue {
  connector: 'AND' | 'OR';
  filters: Array<{
    id?: number | string;
    connector?: 'AND' | 'OR';
    filter?: any;
    [key: string]: any;
  }>;
}
```

#### 分群项接口 (SegmentItem)

```typescript
interface SegmentItem {
  id: number | string;
  name: string;
  description?: string;
  rule?: any;
  type?: string;
  status?: string;
  calcStatus?: string;
  createTime?: string;
  updateTime?: string;
  filter?: any;
}
```

---

### 6. Customize 组件

**描述：** 自定义组合组件，将Filter和Label组件组合在一起。

#### Props (CustomizeProps)

```typescript
interface CustomizeProps {
  /** 自定义组件的值，包含filterInfo和label */
  value?: CustomizeValue;
  
  /** 数据提供器 */
  dataProvider: DataProvider;
  
  /** 值改变时的回调函数 */
  onChange?: (value: CustomizeValue) => void;
  
  /** 组件模式 @default 'edit' */
  mode?: 'edit' | 'detail';
  
  /** 是否为用户分群模式 */
  isUserGroup?: boolean;
}
```

#### 自定义组件值结构 (CustomizeValue)

```typescript
interface CustomizeValue {
  connector: 'AND' | 'OR';
  filterInfo: FilterValue;
  label: FilterValue;
}
```

---

### 7. FlowEditor 组件

**描述：** 流程编辑器组件，提供可视化流程编辑功能。

#### Props (FlowEditorProps)

```typescript
interface FlowEditorProps {
  /** 数据提供器，需包含getFlowBoxNodes方法 */
  dataProvider: FlowDataProvider;
  
  /** 当前流程数据 */
  value?: any;
  
  /** 流程数据改变时的回调 */
  onChange?: (value: any) => void;
  
  /** 画布模式 */
  mode?: 'edit' | 'detail' | 'preview' | 'template';
  
  /** 编辑节点时的回调 */
  onEditNode?: (value?: any) => void;
  
  /** 点击节点时的回调 */
  onClickNode?: (value?: any) => void;
  
  /** 是否开启调试模式 */
  debug?: boolean;
  
  /** 画布配置 */
  canvasConfig?: CanvasConfig;
  
  /** 是否开启缩放功能 */
  grabbing?: boolean | GrabbingBoxProps;
}
```

#### 流程数据提供器 (FlowDataProvider)

```typescript
interface FlowDataProvider {
  /** 获取流程节点列表 */
  getFlowBoxNodes: () => FlowNode[] | Promise<FlowNode[]>;
  
  /** 获取时间节点数据（可选） */
  getAtTimeNodesData?: () => Record<string, number> | Promise<Record<string, number>>;
}
```

#### 流程节点接口 (FlowNode)

```typescript
interface FlowNode {
  id: string;
  name: string;
  type?: string;
  displayName?: string;
  displayInBox?: boolean;
  children?: FlowNode[];
  [key: string]: any;
}
```

---

### 8. SelectTime 组件

**描述：** 时间选择器组件V1，支持相对时间和绝对时间选择。

#### Props (SelectTimeProps)

```typescript
interface SelectTimeProps {
  /** 初始数据，包含时间信息 */
  data: SelectTimeConfigPair;
  
  /** 时间变化时的回调函数，flag为true代表校验不通过 */
  onChange?: (value: any, flag: boolean) => void;
  
  /** 时间选择器的类型 */
  type?: string;
  
  /** 是否显示时间 */
  showTime?: boolean;
  
  /** 自定义样式 */
  style?: any;
  
  /** 是否为分析用时间组件 */
  isAnalysis?: boolean;
  
  /** 是否为行为聚合分群中使用 */
  isActionCollection?: boolean;
}
```

#### 时间配置接口

```typescript
interface BaseSelectTimeConfig {
  type: 'RELATIVE' | 'NOW' | 'ABSOLUTE';
  times: number;
  timeTerm: 'DAY' | 'WEEK' | 'MONTH';
  isPast: boolean;
  truncateAsDay?: boolean;
  isActionCollection?: boolean;
}

interface AbsoluteSelectTimeConfig extends BaseSelectTimeConfig {
  type: 'ABSOLUTE';
  timestamp: number;
}

type SelectTimeConfig = RelativeOrNowSelectTimeConfig | AbsoluteSelectTimeConfig;
type SelectTimeConfigPair = [] | [SelectTimeConfig, SelectTimeConfig];
```

---

### 9. SelectTimeV2 组件

**描述：** 时间选择器组件V2，提供更复杂的时间选择功能。

#### Props (SelectTimeV2Props)

```typescript
interface SelectTimeV2Props {
  /** 时间值数组 */
  value?: any[];
  
  /** 时间改变回调 */
  onChange?: (value: any[]) => void;
}
```

#### 时间配置接口V2 (TimeConfigV2)

```typescript
interface TimeConfigV2 {
  type: 'ABSOLUTE' | 'RELATIVE';
  timestamp?: number;
  times?: number;
  timeTerm?: 'day' | 'week' | 'month';
  startSpecificTime?: 'specificTime' | 'inPast' | 'future';
  endSpecificTime?: 'today' | 'yesterday' | 'inPast' | 'future';
}
```

---

### 10. GrabbingBox 组件

**描述：** 缩放抓取容器组件，为子组件提供缩放、拖拽功能。

#### Props (GrabbingBoxProps)

```typescript
interface GrabbingBoxProps {
  /** 最大缩放比例（百分比） @default 200 */
  maxScale?: number;
  
  /** 最小缩放比例（百分比） @default 20 */
  minScale?: number;
  
  /** 初始缩放比例（百分比） @default 100 */
  initScale?: number;
  
  /** 是否显示缩放按钮 @default true */
  scaleButtons?: boolean;
  
  /** 缩放按钮位置 @default 'top right' */
  scaleButtonsPosition?: string;
  
  /** 缩放按钮水平间距 @default '70px' */
  scaleButtonsSpaceX?: string;
  
  /** 缩放按钮垂直间距 @default '100px' */
  scaleButtonsSpaceY?: string;
  
  /** 缩放步进值 @default 10 */
  scaleStep?: number;
  
  /** 鼠标滚动轮上下滚动倍速 1-5 @default 1 */
  scrollSpeed?: number;
  
  /** 限制的距离，单位px @default 20 */
  limitDistance?: number;
  
  /** 按钮样式 */
  buttonStyle?: React.CSSProperties;
  
  /** 按钮提示属性配置 */
  buttonTipProps?: TooltipProps;
  
  /** 任何显示效果的变化都会触发 */
  onUpdate?: (data: BoxData) => void;
  
  /** 放大显示效果的时候触发 */
  onZoomIn?: (data: BoxData) => void;
  
  /** 缩小显示效果的时候触发 */
  onZoomOut?: (data: BoxData) => void;
  
  /** 改变横向或竖向位置的时候触发 */
  onMove?: (data: any) => void;
  
  /** 重置时触发 */
  onReset?: () => void;
  
  /** 子组件 */
  children?: React.ReactNode;
}
```

#### 盒子数据接口 (BoxData)

```typescript
interface BoxData {
  scale: number;
  translateX: number;
  translateY: number;
}
```

---

## 完整使用示例

### 基础过滤器示例

```typescript
import React, { useState, useRef } from 'react';
import { Filter } from 'wolf-static-cpnt';
import type { FilterProps, FilterRef, FilterValue, DataProvider } from 'wolf-static-cpnt';

const FilterExample: React.FC = () => {
  const filterRef = useRef<FilterRef>(null);
  const [filterValue, setFilterValue] = useState<FilterValue>();

  const dataProvider: DataProvider = {
    getPropertyList: async (name) => {
      // 模拟获取属性列表
      return [
        {
          tableId: 1,
          schemaId: 1,
          fieldName: 'name',
          field: 'user_name',
          fieldType: 'STRING',
          level1: 'user',
          level2: 'profile'
        }
      ];
    },
    getPropertyEnumList: async (tableId, schemaId) => {
      // 模拟获取枚举值
      return ['option1', 'option2', 'option3'];
    }
  };

  const handleValidate = () => {
    if (filterRef.current) {
      const isValid = filterRef.current.isValid(true);
      console.log('Filter is valid:', isValid);
    }
  };

  return (
    <div>
      <Filter
        ref={filterRef}
        dataProvider={dataProvider}
        value={filterValue}
        onChange={(value, innerValue) => {
          setFilterValue(value);
          console.log('Filter changed:', value);
        }}
        mode="edit"
        className="my-filter"
      />
      <button onClick={handleValidate}>验证过滤器</button>
    </div>
  );
};
```

### 行为集合示例

```typescript
import React, { useState } from 'react';
import { ActionCollective } from 'wolf-static-cpnt';
import type { ActionCollectiveProps, ActionCollectiveValue, ActionCollectiveDataProvider } from 'wolf-static-cpnt';

const ActionCollectiveExample: React.FC = () => {
  const [value, setValue] = useState<ActionCollectiveValue>();

  const dataProvider: ActionCollectiveDataProvider = {
    getPropertyList: async (name) => {
      // 返回属性列表
      return [];
    },
    getEventList: async () => {
      // 返回事件列表
      return [];
    },
    getLabelList: async () => {
      // 返回标签列表
      return [];
    },
    getSegmentList: async () => {
      // 返回分群列表
      return [];
    }
  };

  return (
    <ActionCollective
      dataProvider={dataProvider}
      value={value}
      onChange={(newValue) => {
        setValue(newValue);
        console.log('ActionCollective changed:', newValue);
      }}
      mode="edit"
      isActionCollection={true}
    />
  );
};
```

### 流程编辑器示例

```typescript
import React, { useState } from 'react';
import { FlowEditor } from 'wolf-static-cpnt';
import type { FlowEditorProps, FlowDataProvider, FlowNode } from 'wolf-static-cpnt';

const FlowEditorExample: React.FC = () => {
  const [flowData, setFlowData] = useState<any>();

  const dataProvider: FlowDataProvider = {
    getFlowBoxNodes: async () => {
      const nodes: FlowNode[] = [
        {
          id: 'node1',
          name: '开始节点',
          type: 'start',
          displayName: '开始',
          children: []
        }
      ];
      return nodes;
    }
  };

  return (
    <FlowEditor
      dataProvider={dataProvider}
      value={flowData}
      onChange={(value) => {
        setFlowData(value);
        console.log('Flow changed:', value);
      }}
      mode="edit"
      onEditNode={(node) => {
        console.log('Edit node:', node);
      }}
      grabbing={true}
    />
  );
};
```

---

## 最佳实践

### 1. 数据提供器设计

- 确保 `getPropertyList` 方法返回正确的属性格式
- 对于异步数据，使用 Promise 返回
- 考虑数据缓存以提高性能

### 2. 类型安全

- 始终使用 TypeScript 类型定义
- 为自定义数据提供器扩展接口
- 利用类型检查避免运行时错误

### 3. 性能优化

- 使用 React.memo 包装组件避免不必要的重渲染
- 合理使用 useCallback 和 useMemo
- 对大量数据进行虚拟化处理

### 4. 错误处理

- 在数据提供器中添加错误处理
- 使用组件的 `isValid` 方法进行数据校验
- 提供友好的错误提示

---

## 工具函数

### setTheme(theme)
设置组件主题

### cpntConvertToCSV(data)
将数据转换为CSV格式

### cpntGetNewLang()
获取最新语言包

---

- 更新时间：2025年7月
- 支持 TypeScript：✅