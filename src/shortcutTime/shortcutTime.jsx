/*
 * @Author: Wxw
 * @Date: 2022-09-07 10:11:40
 * @LastEditTime: 2022-10-18 16:23:44
 * @LastEditors: Wxw
 * @Description: 快捷时间选项
 * @FilePath: \datatist-wolf-static-cpnt\src\shortcutTime\shortcutTime.jsx
 */
import React, { useEffect, useState } from 'react';
import { Popover } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './shortcutTime.scss';
import { t } from '../utils/translation';

/** @typedef {import('./types').ShortcutTimeProps} ShortcutTimeProps */
/** @typedef {import('./types').ShortcutTimeOption} ShortcutTimeOption */

const icon = (
  <svg
    viewBox="64 64 896 896"
    focusable="false"
    data-icon="calendar"
    width="1em"
    height="1em"
    fill="currentColor"
    aria-hidden="true"
  >
    <path d="M880 184H712v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H384v-64c0-4.4-3.6-8-8-8h-56c-4.4 0-8 3.6-8 8v64H144c-17.7 0-32 14.3-32 32v664c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V216c0-17.7-14.3-32-32-32zm-40 656H184V460h656v380zM184 392V256h128v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h256v48c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-48h128v136H184z" />
  </svg>
);
const shortcutOptions = {
  [t('cpnt-ZQRePXsNBZsJ')]: [dayjs().startOf('day'), dayjs()],
  [t('cpnt-LAJcXjntbCIm')]: [dayjs().subtract(1, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  [t('cpnt-9ItqdniwfgLm')]: [dayjs().startOf('week'), dayjs()],
  [t('cpnt-GxdhkRBTNV5o')]: [dayjs().subtract(1, 'weeks').startOf('week'), dayjs().subtract(1, 'weeks').endOf('week')],
  [t('cpnt-xJXqztvGp904')]: [dayjs().startOf('month'), dayjs()],
  [t('cpnt-zEIrdxpDbnrS')]: [
    dayjs().subtract(1, 'months').startOf('month'),
    dayjs().subtract(1, 'months').endOf('month')
  ],
  [t('cpnt-JIzOftRaUrLt')]: [dayjs().startOf('year'), dayjs()],
  [t('cpnt-zGKZkPIY5wmA')]: [dayjs().subtract(1, 'years').startOf('year'), dayjs().subtract(1, 'years').endOf('year')],
  // 过去1小时: [dayjs().subtract(1, 'hours').startOf('hour'), dayjs().subtract(1, 'hours').endOf('hour')],
  // 过去24小时: [dayjs().subtract(1, 'hours').subtract(24, 'hours').startOf('hour'), dayjs().subtract(1, 'hours').startOf('hour')],
  [t('cpnt-qpMO14GcqGhk')]: [
    dayjs().subtract(1, 'days').subtract(6, 'days').startOf('day'),
    dayjs().subtract(1, 'days').endOf('day')
  ],
  [t('cpnt-WJfaf1arZhYm')]: [dayjs().subtract(14, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  [t('cpnt-NqdAsz5ZsOZW')]: [dayjs().subtract(30, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  [t('cpnt-XVJHPeo4wYiE')]: [dayjs().subtract(60, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  [t('cpnt-WtAFUsztlp24')]: [dayjs().subtract(90, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')],
  [t('cpnt-xRWjsl58VJ7I')]: [dayjs().subtract(180, 'days').startOf('day'), dayjs().subtract(1, 'days').endOf('day')]
};

/**
 * 快捷时间选项组件
 * @param {ShortcutTimeProps} props 组件属性
 * @returns {React.ReactElement}
 */
const ShortCutTime = ({ setTime, isAnalysis, dispatch, dispatchName, clearName, onChange }) => {
  const [visible, setVisible] = useState(false);
  const [renderName, setRenderName] = useState(null);

  useEffect(() => {
    setRenderName(null);
  }, [clearName]);

  const shortcut = () => {
    return (
      <div className="shortcutOptions">
        {Object.keys(shortcutOptions).map((item) => {
          return (
            <span key={item} className="shortcutOptions-item" onClick={() => clickShortcut(item)}>
              {item}
            </span>
          );
        })}
      </div>
    );
  };

  const clickShortcut = (name) => {
    const range = shortcutOptions[name];
    const _info = [
      {
        type: 'ABSOLUTE',
        timestamp: dayjs(range[0]).valueOf(),
        truncateAsDay: !!isAnalysis
      },
      {
        type: 'ABSOLUTE',
        timestamp: dayjs(range[1]).valueOf(),
        truncateAsDay: !!isAnalysis
      }
    ];
    dispatch && dispatch({ [dispatchName]: _info });
    setTime && setTime(_info);
    onChange && onChange(_info);
    setVisible(false);
    setRenderName(name);
  };

  return (
    <div className="shortcutTime">
      <Popover
        content={shortcut}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        trigger="click"
        open={visible}
        className="shortcut"
        overlayStyle={{ width: '227px' }}
        onOpenChange={setVisible}
      >
        <div>
          <div className="icons">
            <span className="icon">{icon}</span>
            <DownOutlined height="32px" />
          </div>
          {renderName && (
            <div className="showTimes">
              <span>{renderName}</span>
            </div>
          )}
        </div>
      </Popover>
    </div>
  );
};
export default ShortCutTime;
