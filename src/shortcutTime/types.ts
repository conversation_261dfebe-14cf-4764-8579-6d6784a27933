import { CSSProperties } from 'react';
import { SelectTimeConfig } from '../selectTime/types';

/**
 * 快捷时间选项接口
 */
export interface ShortcutTimeOption {
  label: string;
  value: string;
  data: [SelectTimeConfig, SelectTimeConfig];
}

/**
 * ShortcutTime组件Props接口
 */
export interface ShortcutTimeProps {
  /**
   * @description 组件样式
   */
  style?: CSSProperties;

  /**
   * @description 清除名称标识
   */
  clearName?: number;

  /**
   * @description 时间改变回调
   */
  onChange?: (value: [SelectTimeConfig, SelectTimeConfig], flag?: boolean) => void;

  /**
   * @description 设置时间回调
   */
  setTime?: (value: [SelectTimeConfig, SelectTimeConfig]) => void;

  /**
   * @description 是否为分析模式
   */
  isAnalysis?: boolean;
}
