import React, { useState, useEffect, useContext } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Input } from 'antd';
import FilterConfig from './FilterConfig';
import { t } from '../utils/translation';
import FilterContext from './FilterContext';

const { operatorList, userGroupOperatorList, typeOperator, userGroupTypeOperator } = FilterConfig;

export default function FilterOperator({ value, onChange }) {
  const { isUserGroup } = useContext(FilterContext);
  // 操作符map
  const OPERATOR_MAP = (isUserGroup ? userGroupOperatorList : operatorList).reduce((map, obj) => {
    map[obj.operator] = obj;
    return map;
  }, {});

  const [searchText, setSearchText] = useState(OPERATOR_MAP[value.operator]?.name || '');
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else if (getTimeslicingName() || OPERATOR_MAP[value.operator]) {
      setSearchText(getTimeslicingName() || OPERATOR_MAP[value.operator].name);
    }
  }, [value.operator, menuVisible]);

  useEffect(() => {
    setSearchText(OPERATOR_MAP[value.operator]?.name || getTimeslicingName() || '');
  }, [value.operator]);

  const operatorMenu = () => {
    const munuList = getTypeOperators()
      .map(v => OPERATOR_MAP[v])
      .filter(v => !searchText || v?.name.indexOf(searchText) >= 0)
      .filter(v => v);
    return munuList.map((p, i) => {
      if (p.operator === 'TIMESLICING') {
        return (
          <Menu.SubMenu key={i} title={p?.name}>
            {p?.children.map((child, j) => (
              <Menu.Item key={j} onClick={() => onSelect(child)}>
                {child?.name}
              </Menu.Item>
            ))}
          </Menu.SubMenu>
        );
      }
      return (
        <Menu.Item key={i} onClick={() => onSelect(p)}>
          {p?.name}
        </Menu.Item>
      );
    });
  };

  const getTimeslicingName = () => {
    return OPERATOR_MAP?.TIMESLICING?.children.find(v => v.operator === value.operator)?.name || '';
  };

  const onSelect = oeprator => {
    value.changeOperator(oeprator.operator);
    onChange(value);
    setSearchText(OPERATOR_MAP[value.operator]?.name || getTimeslicingName());
  };

  const getTypeOperators = () => {
    if (!isUserGroup) {
      return typeOperator[value.fieldType || 'STRING'];
    } else {
      return userGroupTypeOperator[value.fieldType || 'STRING'];
    }
  };

  const menu = () => {
    return (
      <Menu forceSubMenuRender style={{ maxHeight: 400, overflowY: 'auto', maxWidth: 500, overflowX: 'auto' }}>
        {value?.fieldType && operatorMenu()}
      </Menu>
    );
  };

  const onMenuVisible = v => {
    if (v) {
      setSearchText('');
    }
    setMenuVisible(v);
  };

  return (
    <Dropdown getPopupContainer={triggerNode => triggerNode.parentNode} overlay={menu} trigger={['click']} onOpenChange={onMenuVisible}>
      <div className="clickWrapper">
        <Input
          className="ant-dropdown-link"
          placeholder={t('cpnt-n4qcTXxDt5yw')}
          // suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={e => setSearchText((e.target.value))}
          onFocus={event => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
