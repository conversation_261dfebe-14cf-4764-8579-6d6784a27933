/**
 * 分群项数据结构
 */
export interface SegmentItem {
  /** 分群ID */
  id: number | string;
  /** 分群名称 */
  name: string;
  /** 分群描述 */
  description?: string;
  /** 分群规则 */
  rule?: any;
  /** 分群类型 */
  type?: string;
  /** 状态 */
  status?: string;
  /** 计算状态 */
  calcStatus?: string;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 过滤器配置 */
  filter?: any;
}

/**
 * 复合过滤器值数据结构
 */
export interface ComplexValue {
  /** 连接符 */
  connector: 'AND' | 'OR';
  /** 过滤器列表 */
  filters: Array<{
    /** 分群ID */
    id?: number | string;
    /** 连接符 */
    connector?: 'AND' | 'OR';
    /** 过滤器配置 */
    filter?: any;
    /** 其他属性 */
    [key: string]: any;
  }>;
}

/**
 * 复合过滤器数据提供器
 */
export interface ComplexDataProvider {
  /** 获取属性列表 */
  getPropertyList?: (_name?: string) => Property[] | Promise<Property[]>;
  /** 获取属性枚举列表 */
  getPropertyEnumList?: (tableId: number, schemaId: number) => PropertyEnum[] | Promise<PropertyEnum[]>;
  /** 获取标签信息 */
  getLabelInfo?: (id: number) => any | Promise<any>;
  /** 获取事件数量日志 */
  getEventCountLogsByProjectId?: () => any[] | Promise<any[]>;
  /** 获取分组列表 */
  getGroupList?: () => any[] | Promise<any[]>;
}

/**
 * 属性定义
 */
export interface Property {
  /** 属性ID */
  id: number;
  /** 属性名称 */
  name: string;
  /** 属性显示名称 */
  displayName: string;
  /** 属性类型 */
  type: string;
  /** 表ID */
  tableId?: number;
  /** 模式ID */
  schemaId?: number;
}

/**
 * 属性枚举值
 */
export interface PropertyEnum {
  /** 枚举值 */
  value: string | number;
  /** 枚举显示名称 */
  label: string;
}

/**
 * 复合过滤器组件属性
 */
export interface ComplexProps {
  /** 过滤器值 */
  value?: ComplexValue;
  /** 数据提供器 */
  dataProvider: ComplexDataProvider;
  /** 值变化回调 */
  onChange?: (value: ComplexValue, rawValue?: any) => void;
  /** 组件模式 */
  mode?: 'edit' | 'detail';
  /** 分群选择列表 */
  selectList?: SegmentItem[];
  /** 是否为用户分群 */
  isUserGroup?: boolean;
  /** 样式类名 */
  className?: string;
}

/**
 * 复合过滤器组件引用
 */
export interface ComplexRef {
  /** 验证过滤器是否有效 */
  isValid: (flag?: boolean) => boolean;
} 