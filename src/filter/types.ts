/**
 * 数据提供器接口
 */
export interface DataProvider {
  /**
   * @description 获取属性列表
   * @param name 属性名称（可选）
   * @returns 属性列表
   */
  getPropertyList: (name?: string) => Property[] | Promise<Property[]>;

  /**
   * @description 获取属性枚举值列表
   * @param _tableId 表ID
   * @param _schemaId 模式ID
   * @returns 枚举值列表
   */
  getPropertyEnumList?: (_tableId: number, _schemaId: number) => any[] | Promise<any[]>;
}

/**
 * 属性接口
 */
export interface Property {
  tableId: number;
  schemaId: number;
  fieldName: string;
  field: string;
  fieldType: 'STRING' | 'INT' | 'FLOAT' | 'DATE' | 'DATETIME' | 'BOOLEAN' | 'HIVE_DATE' | 'HIVE_TIMESTAMP';
  level1?: string;
  level2?: string;
  isEnum?: boolean;
}

/**
 * 过滤条件接口
 */
export interface FilterCondition {
  tableId: number;
  schemaId: number;
  fieldName: string;
  field: string;
  fieldType: string;
  level1?: string;
  level2?: string;
  operator:
    | 'EQ'
    | 'NOT_EQ'
    | 'GT'
    | 'GTE'
    | 'LT'
    | 'LTE'
    | 'BETWEEN'
    | 'IN'
    | 'NOT_IN'
    | 'LIKE'
    | 'NOT_LIKE'
    | 'IS_NULL'
    | 'IS_NOT_NULL';
  value?: any;
  isEnum?: boolean;
}

/**
 * 过滤组接口
 */
export interface FilterGroup {
  connector: 'AND' | 'OR';
  filters: FilterCondition[];
}

/**
 * 过滤器值接口
 */
export interface FilterValue {
  connector: 'AND' | 'OR';
  filters: FilterGroup[];
}

/**
 * Filter组件Props接口
 */
export interface FilterProps {
  /**
   * @description 过滤器的值，包含filters数组
   */
  value?: FilterValue;

  /**
   * @description 数据提供器，需包含getPropertyList方法
   */
  dataProvider: DataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (_value: FilterValue, _innerValue: FilterValue) => void;

  /**
   * @description 组件模式，edit可编辑，detail只读
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否隐藏添加按钮
   * @default false
   */
  hideAdd?: boolean;

  /**
   * @description 是否隐藏初始过滤条件
   * @default false
   */
  hideInit?: boolean;

  /**
   * @description 自定义CSS类名
   */
  className?: string;

  /**
   * @description 添加按钮的文本
   */
  addButtonText?: string;

  /**
   * @description 是否为用户分群模式
   */
  isUserGroup?: boolean;
}

/**
 * Filter组件Ref方法接口
 */
export interface FilterRef {
  /**
   * @description 校验过滤器是否有效
   * @param _flag 是否显示验证错误
   * @returns 是否有效
   */
  isValid: (_flag?: boolean) => boolean;

  /**
   * @description 添加新的过滤组
   * @returns 新的过滤器值
   */
  addFilterGroup: () => FilterValue;

  /**
   * @description 获取当前过滤器的值
   * @returns 当前值
   */
  getValue: () => FilterValue;

  /**
   * @description 获取过滤条件总数
   * @returns 总数
   */
  getFilterCount: () => number;
}
