import _ from 'lodash';
import filterLocales from '../filter/locales';
import eventLocales from '../event/locales';
import labelLocales from '../label/locales';
import actioncollectiveLocales from '../actioncollective/locales';
import customizeLocales from '../customize/locales';
import newactioncollectiveLocales from '../newactioncollective/locales';
import neweventLocales from '../newevent/locales';
import newfilterLocales from '../newfilter/locales';
import segmentLocales from '../segment/locales';
import selectTimeLocales from '../selectTime/locales';
import selectTimeV2Locales from '../selectTimeV2/locales';
import shortcutTimeLocales from '../shortcutTime/locales';
import flowLocales from '../flow/locales';
import grabbingLocales from '../grabbing/locales';

// 合并所有模块的翻译
const mergeTranslations = (lang: string[] = ['zh_CN', 'en_US']) => {
  const modules = [
    filterLocales,
    eventLocales,
    labelLocales,
    actioncollectiveLocales,
    customizeLocales,
    newactioncollectiveLocales,
    neweventLocales,
    newfilterLocales,
    segmentLocales,
    selectTimeLocales,
    selectTimeV2Locales,
    shortcutTimeLocales,
    flowLocales,
    grabbingLocales
  ];

  // 初始化结果对象，只包含传入的语言
  const result: Record<string, { translation: Record<string, any> }> = {};
  lang.forEach((language) => {
    result[language] = { translation: {} };
  });

  // 深度合并所有模块的翻译，但只处理传入的语言
  modules.forEach((moduleLocale: any) => {
    lang.forEach((language: any) => {
      if (moduleLocale[language]) {
        result[language] = result[language] || { translation: {} };
        // @ts-ignore
        result[language].translation = _.merge(result[language].translation, moduleLocale[language]);
      }
    });
  });

  return result;
};

export default mergeTranslations;
