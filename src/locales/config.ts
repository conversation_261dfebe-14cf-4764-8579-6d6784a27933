import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import mergeTranslations from './mergeTranslations';

const initLang = () => {
  const lang = ['zh_CN', 'en_US'].includes(localStorage.getItem('lang')!) ? localStorage.getItem('lang')! : 'zh_CN';
  // @ts-ignore
  i18n.use(initReactI18next).init(
    {
      fallbackLng: lang as string,
      resources: mergeTranslations(['zh_CN', 'en_US']),
      debug: false,
      interpolation: {
        escapeValue: false
      }
    },
    (err) => {
      if (err) {
        console.error(err);
      } else {
        console.log('cpnt i18n初始化');
      }
    }
  );
};

initLang();

// const cn = i18n.getResourceBundle('zh_CN', 'translation');
// const en = i18n.getResourceBundle('en_US', 'translation');

// console.log(cn, en, 'cn 和 en 对比');

// console.log(localStorage.getItem('lang'), i18n.language, '缓存lang 和i18 lang对比');
