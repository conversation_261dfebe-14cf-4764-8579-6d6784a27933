import { Dayjs } from 'dayjs';

/**
 * 时间配置接口V2
 */
export interface TimeConfigV2 {
  type: 'ABSOLUTE' | 'RELATIVE';
  timestamp?: number;
  times?: number;
  timeTerm?: 'day' | 'week' | 'month';
  startSpecificTime?: 'specificTime' | 'inPast' | 'future';
  endSpecificTime?: 'today' | 'yesterday' | 'inPast' | 'future';
}

/**
 * SelectTimeV2组件Props接口
 */
export interface SelectTimeV2Props {
  /**
   * @description 时间值数组
   */
  value?: TimeConfigV2[];

  /**
   * @description 时间改变回调
   */
  onChange?: (value: TimeConfigV2[]) => void;
}

/**
 * 快捷选项接口
 */
export interface ShortcutOption {
  [key: string]: [Dayjs, Dayjs];
}

/**
 * 内部状态接口
 */
export interface SelectTimeV2State {
  loading: boolean;
  tabKey: 'RELATIVE' | 'ABSOLUTE';
  visibleConversion: boolean;
  visibleIcon: boolean;
  value: [Dayjs, Dayjs];
  radio: string;
  inPastValue: {
    startSpecificTime: string;
    startType: string;
    startValue: number;
    endValue: number;
    endType: string;
    endSpecificTime: string;
  };
  info: any;
}
