// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, { forwardRef, useImperativeHandle, useRef } from 'react';
import { Divider } from 'antd';
import ComplexGroup from './ComplexGroup';
import Filter from '../filter/Filter';
import Label from '../label/Filter';
import './filter.scss';

/** @typedef {import('./types').CustomizeProps} CustomizeProps */
/** @typedef {import('./types').CustomizeRef} CustomizeRef */

/** @type {React.ForwardRefExoticComponent<CustomizeProps & React.RefAttributes<CustomizeRef>>} */
const Customize = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    isValid() {
      const filterFlag = filterRef.current.isValid(true);
      const labelFlag = labelRef.current.isValid(true);
      return filterFlag && labelFlag;
    }
  }));

  const { dataProvider, onChange, mode, value, isUserGroup } = props;
  const labelRef = useRef(null);
  const filterRef = useRef(null);

  const customizeChange = (datas, type) => {
    const result = { ...value, [type]: datas };
    onChange && onChange(result);
  };

  const count = () => {
    if (mode === 'edit') {
      return 2;
    }
    let num = 0;
    if (
      value.filterInfo &&
      value.filterInfo.filters &&
      value.filterInfo.filters.length > 0
    ) {
      num += 1;
    }
    if (value.label && value.label.filters && value.label.filters.length > 0) {
      num += 1;
    }
    return num;
  };

  return (
    <div className="wolf-static-component_filter_FilterGroupPanel_customize">
      <ComplexGroup
        connector={value.connector}
        onChangeConnector={res => customizeChange(res, 'connector')}
        filterCount={count()}
        inner="inner"
        mode={mode}
      >
        <Filter
          dataProvider={dataProvider}
          value={value?.filterInfo || {}}
          onChange={res => customizeChange(res, 'filterInfo')}
          mode={mode}
          ref={filterRef}
          isUserGroup={isUserGroup}
        />
        {count() >= 2 && <Divider />}
        <Label
          showInitLine={false}
          dataProvider={dataProvider}
          value={value?.label || {}}
          onChange={res => customizeChange(res, 'label')}
          mode={mode}
          ref={labelRef}
          isUserGroup={isUserGroup}
        />
      </ComplexGroup>
    </div>
    // </FilterContext.Provider>
  );
});

export default Customize;
