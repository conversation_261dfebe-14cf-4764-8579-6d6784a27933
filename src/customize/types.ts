import { FilterValue, DataProvider } from '../filter/types';

/**
 * 自定义组件值接口
 */
export interface CustomizeValue {
  connector: 'AND' | 'OR';
  filterInfo: FilterValue;
  label: FilterValue;
}

/**
 * Customize组件Props接口
 */
export interface CustomizeProps {
  /**
   * @description 自定义组件的值，包含filterInfo和label
   */
  value?: CustomizeValue;

  /**
   * @description 数据提供器
   */
  dataProvider: DataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (value: CustomizeValue) => void;

  /**
   * @description 组件模式
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否为用户分群模式
   */
  isUserGroup?: boolean;
}

/**
 * Customize组件Ref方法接口
 */
export interface CustomizeRef {
  /**
   * @description 校验Filter和Label是否都有效
   * @returns 是否有效
   */
  isValid: () => boolean;
}
