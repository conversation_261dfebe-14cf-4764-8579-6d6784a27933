import { FilterValue } from '../filter/types';
import { EventDataProvider } from '../event/types';

/**
 * 标签接口
 */
export interface Label {
  id: number | string;
  name: string;
  [key: string]: any;
}

/**
 * 行为集合数据提供器接口
 */
export interface ActionCollectiveDataProvider extends EventDataProvider {
  /**
   * @description 获取标签列表
   * @returns 标签列表
   */
  getLabelList?: () => Label[] | Promise<Label[]>;

  /**
   * @description 获取细分列表
   * @returns 细分列表
   */
  getSegmentList?: () => any[] | Promise<any[]>;
}

/**
 * 行为集合过滤器项接口
 */
export interface ActionCollectiveFilterItem {
  connector: 'AND' | 'OR';
  eventGroup?: FilterValue;
  userProperty?: FilterValue;
  userLabel?: FilterValue;
  segment?: FilterValue;
}

/**
 * 行为集合值接口
 */
export interface ActionCollectiveValue {
  connector: 'AND' | 'OR';
  filters: ActionCollectiveFilterItem[];
}

/**
 * ActionCollective组件Props接口
 */
export interface ActionCollectiveProps {
  /**
   * @description 行为集合的值
   */
  value?: ActionCollectiveValue;

  /**
   * @description 数据提供器
   */
  dataProvider: ActionCollectiveDataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (value: ActionCollectiveValue) => void;

  /**
   * @description 组件模式
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否显示初始行
   */
  showInitLine?: boolean;

  /**
   * @description 是否为行为集合分群模式
   */
  isActionCollection?: boolean;

  /**
   * @description 是否为用户分群模式
   */
  isUserGroup?: boolean;
}

/**
 * ActionCollective组件Ref方法接口
 */
export interface ActionCollectiveRef {
  /**
   * @description 校验所有子组件是否有效
   * @returns 是否有效
   */
  isValid: () => boolean;
}
