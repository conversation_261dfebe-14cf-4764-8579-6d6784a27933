/* eslint-disable no-unused-vars */
import React, { useContext } from 'react';
import { Switch } from 'antd';
import FilterContext from './FilterContext';
import FilterConfig from './FilterConfig';
import { t } from '../utils/translation';

// 连接器
const FILTER_CONNECTOR = FilterConfig.connector;
// 连接器map
const FILTER_CONNECTOR_SWITCH_MAP = {
  true: FILTER_CONNECTOR[0],
  false: FILTER_CONNECTOR[1]
};

const FILTER_CONNECTOR_SWITCH_MAP_REVERSE = {};
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[0].value] = true;
FILTER_CONNECTOR_SWITCH_MAP_REVERSE[FILTER_CONNECTOR[1].value] = false;

const FilterConnector = ({ value, onChange }) => {
  const { logProvider, mode } = useContext(FilterContext);
  const log = logProvider.getLogger('FilterConnector');

  log.debug('value', value);

  return (
    <div className="filter-connector-and">{t('cpnt-rAevvR3Vjdin')}</div>
    // <Switch
    //   size="small"
    //   checkedChildren={FILTER_CONNECTOR_SWITCH_MAP.true.name}
    //   unCheckedChildren={FILTER_CONNECTOR_SWITCH_MAP.false.name}
    //   checked={FILTER_CONNECTOR_SWITCH_MAP_REVERSE[value]}
    //   onChange={v => onChange(FILTER_CONNECTOR_SWITCH_MAP[v].value)}
    //   // disabled={mode === 'detail'}
    //   disabled
    // />
  );
};

export default FilterConnector;
