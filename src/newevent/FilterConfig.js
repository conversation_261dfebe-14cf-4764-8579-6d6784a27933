import { t } from '../utils/translation';

export default {
  maxFilterCount: 5,
  operatorList: [
    {
      name: t('cpnt-kxF3zvOs5CRr'),
      operator: 'EQ'
    },
    {
      name: t('cpnt-jOhgLs5cMvpE'),
      operator: 'NE'
    },
    // {
    //   name: '大于',
    //   operator: 'GT'
    // }, {
    //   name: '大于等于',
    //   operator: 'GTE'
    // }, {
    //   name: '小于',
    //   operator: 'LT'
    // }, {
    //   name: '小于等于',
    //   operator: 'LTE'
    // }, {
    //   name: '范围',
    //   operator: 'BETWEEN'
    // }, {
    //   name: '高级范围',
    //   operator: 'ADVANCED_BETWEEN'
    // },
    {
      name: t('cpnt-HqitC4RtNzL2'),
      operator: 'IN'
    },
    {
      name: t('cpnt-yW9cyaY5pWl0'),
      operator: 'NOT_IN'
    },
    {
      name: t('cpnt-MUh1hCF6kxt7'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('cpnt-H81Avy0woRgT'),
      operator: 'IS_NULL'
    }
    // {
    //   name: '全部',
    //   operator: 'ALL'
    // }, {
    //   name: '匹配',
    //   operator: 'LIKE'
    // }, {
    //   name: '不匹配',
    //   operator: 'NOT_LIKE'
    // }, {
    //   name: '开头匹配',
    //   operator: 'START_WITH'
    // }, {
    //   name: '开头不匹配',
    //   operator: 'NOT_START_WITH'
    // }, {
    //   name: '结尾匹配',
    //   operator: 'END_WITH'
    // }, {
    //   name: '结尾不匹配',
    //   operator: 'NOT_END_WITH'
    // }, {
    //   name: '是',
    //   operator: 'IS_TRUE'
    // }, {
    //   name: '否',
    //   operator: 'IS_FALSE'
    // }
  ],
  // typeOperator: {
  //   INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'ALL', 'LIKE', 'NOT_LIKE', 'START_WITH', 'NOT_START_WITH', 'END_WITH', 'NOT_END_WITH', 'IN', 'NOT_IN'],
  //   BOOL: ['IS_TRUE', 'IS_FALSE'],
  //   LABEL: ['IN', 'NOT_IN', 'ALL']
  // },
  typeOperator: {
    INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN'],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
    LABEL: ['IN', 'NOT_IN', 'ALL']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('cpnt-LOQyQLXMNPnI'),
      value: 'AND'
    },
    {
      name: t('cpnt-DX80n28lVGHl'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-ETeQNvitoqpB')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-0eH3UaKZw1hO'),
        regex: t('cpnt-PYBJf2GamIhl')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-HN4doLXjWPte'),
        regex: t('cpnt-PYBJf2GamIhl')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('cpnt-q7H4sbrLNxhV'),
        maxLen: t('cpnt-HN4doLXjWPte'),
        regex: t('cpnt-hxxUmlZT63AO')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-ZcEqR4qZGHS5')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-ZcEqR4qZGHS5')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-WuNVRvsXMkks')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-TTE9dRIUzlvR')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-WuNVRvsXMkks')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('cpnt-UyVvA3PHHTf5')
      }
    }
  },
  DATE_TYPE_MAP: {
    ABSOLUTE: t('cpnt-7FOXX2fsJbMr'),
    RELATIVE: t('cpnt-99uC197y6SxK')
  },
  relativeTimeObj: {
    0: t('cpnt-Cn7iOgyPkY6x'),
    1: t('cpnt-28pWneMFrtoR'),
    2: t('cpnt-JP7h114oa6nv')
  },
  EVENT_ACTION: {
    DONE: t('cpnt-bRaDymyauQUE'),
    NOT_DO: t('cpnt-5j0MCRL4qE2b')
    // DO_SEQ: '依次做过'
  },
  // CONDITIONFUN: {
  //   INT: [{ name: '去重数', value: 'UNIQUE_COUNT' }, { name: '计数', value: 'COUNT' }, { name: '总和', value: 'SUM' }, { name: '平均值', value: 'AVG' }, { name: '最大值', value: 'MAX' }, { name: '最小值', value: 'MIN' }],
  //   LONG: [{ name: '去重数', value: 'UNIQUE_COUNT' }, { name: '计数', value: 'COUNT' }, { name: '总和', value: 'SUM' }, { name: '平均值', value: 'AVG' }, { name: '最大值', value: 'MAX' }, { name: '最小值', value: 'MIN' }],
  //   DOUBLE: [{ name: '计数', value: 'COUNT' }, { name: '总和', value: 'SUM' }, { name: '平均值', value: 'AVG' }, { name: '最大值', value: 'MAX' }, { name: '最小值', value: 'MIN' }],
  //   TIMES: [{ name: '去重数', value: 'UNIQUE_COUNT' }, { name: '总和', value: 'SUM' }, { name: '平均值', value: 'AVG' }, { name: '最大值', value: 'MAX' }, { name: '最小值', value: 'MIN' }],
  //   STRING: [{ name: '去重数', value: 'UNIQUE_COUNT' }],
  //   TIMESTAMP: [{ name: '计数', value: 'COUNT' }]
  // }
  CONDITIONFUN: {
    INT: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    LONG: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    DOUBLE: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    TIMES: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    STRING: [{ name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' }],
    TIMESTAMP: [
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    BOOL: [{ name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' }],
    HIVE_TIMESTAMP: [
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    HIVE_DATE: [
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    DATE: [
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'VALUE_COUNT' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ]
  }
};
