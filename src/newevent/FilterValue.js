import React, { useState, useContext, useEffect, useRef, Fragment } from 'react';
import { Input, AutoComplete, InputNumber } from 'antd';
import _ from 'lodash';
import useDebounce from '../utils/useDebounce';
import FilterContext from './FilterContext';
import { t } from '../utils/translation';

const FilterValueContext = React.createContext();

let Text = '';

/**
 * 输入一个值的情况
 */
function TextInput(props) {
  let { fieldValue, items, onChange } = props;
  const { setMenuVisible } = useContext(FilterValueContext);
  const inputEl = useRef(null);

  // if (fieldValue === '') {
  //   fieldValue = Text;
  //   value.changeValue(fieldValue);
  // }

  if (_.isArray(items)) {
    items = items.filter(n => !!n.value);
  } else {
    items = [];
  }

  const itemsDs = items.filter(n => !!n.value).map(v => (
    // <Option label={v.value || v} value={v.value || v} key={v.id}>{v.value || v}</Option>
    { value: v.value, label: v.value }
  ));
  return (
    <>
      <AutoComplete
        // dataSource={itemsDs}
        style={{
          width: 100
        }}
        placeholder={t('cpnt-rva2Ysmx7Tkq')}
        showSearch={items.length > 0}
        onChange={onChange}
        value={fieldValue}
        maxLength={36}
        options={itemsDs}
        filterOption={(inputValue, option) => `${option.value}`.toUpperCase().indexOf(typeof inputValue === 'string' ? inputValue.toUpperCase() : '') !== -1}
        // allowClear
        // optionLabelProp="label"
        ref={inputEl}
        onDropdownVisibleChange={v => setMenuVisible(v)}
      /><span> --------</span>
    </>

  );
  // return 'OneInput';
}

/**
 * 单一输入的输入框，要根据fieldType决定显示那种输入框，可能是TextInput, 也可能是DateInput
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleTextInput(props) {
  return TextInput(props);
}

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: 100, marginRight: '5px' }}
        placeholder={t('cpnt-ap2n3s4YJilo')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={value => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('cpnt-ZDcQWGpjO5H7')}
      <InputNumber
        style={{ width: 100, marginLeft: '5px' }}
        placeholder={t('cpnt-raQ4DVxeatXT')}
        value={fieldValue[1]}
        onChange={value => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

// const longToMoment = fv => {
//   return _.isArray(fv)
//     ? fv.map(v => dayjs(v))
//     : undefined;
// };

/**
 * 范围日历输入框
 */
// function DateBetweenInput(props) {
//   let { fieldValue, fieldType, onChange } = props;
//   const [value, setValue] = useState(longToMoment(fieldValue));
//   const showTime = !!(fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP');
//   const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
//   let unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
//   const onValueChange = m => {
//     setValue(m);
//     onChange(m && m[0] && m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]);
//   };

//   // const onValueOk = m => {
//   //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
//   // };

//   return (
//     <RangePicker
//       allowClear={false}
//       showTime={showTime}
//       format={format}
//       placeholder={['开始时间', '结束时间']}
//       // onOk={onValueOk}
//       onChange={onValueChange}
//       value={value}
//     />
//   );
// }

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

// function DateInput(props) {
//   let { fieldType, onChange } = props;

//   const showTime = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? { format: 'HH:mm:ss' } : null;
//   const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
//   let unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
//   const onValueChange = m => {
//     onChange(m.startOf(unit).valueOf());
//   };

//   return (
//     <DatePicker
//       placeholder="请输入日期"
//       showTime={showTime}
//       format={format}
//       // allowClear
//       allowClear={false}
//       value={props.fieldValue && dayjs(props.fieldValue)}
//       // getCalendarContainer={triggerNode => triggerNode.parentNode}
//       onChange={onValueChange}
//     />
//   );
// }

/**
 * 返回
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldValue, onChange } = props;
  const inputEl = useRef(null);
  return <InputNumber
    style={{ width: 100, marginRight: '5px' }}
    placeholder={t('cpnt-LpZ7W30tOq5t')}
    value={fieldValue}
    ref={inputEl}
    min={0}
    onChange={onChange}
  />;
}

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { logProvider } = useContext(FilterContext);
  const { eventAggregateProperty = {} } = value;
  const [items] = useState([]);
  const [log] = useState(logProvider.getLogger('FilterValue'));
  const [, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });
  const { operator, propertyType } = eventAggregateProperty;

  let fieldType = 'STRING';

  if (propertyType === 'TIMES') {
    fieldType = 'INT';
  } else {
    fieldType = eventAggregateProperty?.property?.fieldType || 'STRING';
  }
  const [fieldValue, setFieldValue] = useState(eventAggregateProperty.value);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    setFieldValue(eventAggregateProperty.value);
  }, [eventAggregateProperty.value]);

  useEffect(() => {
    log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = v => {
    if (v === '') {
      Text = '';
    }
    Text = v;
    log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.changeValue(v);
    setFieldValue(v);
  };

  const onTextChange = e => {
    if (e === '') {
      Text = '';
    } else {
      Text = e.target.value;
    }

    value.changeEventAggregateProperty({
      ...value.eventAggregateProperty,
      value: Text
    });
    onChange(value);
  };
  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case '':
        return (
          <SingleTextInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          // value={value}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
      case 'ALL':
        return <span />;
      default:
        return (
          <>
            <Input onChange={onTextChange} style={{ width: '100px' }} placeholder={t('cpnt-rva2Ysmx7Tkq')} maxLength={20} />
          </>

        );
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
