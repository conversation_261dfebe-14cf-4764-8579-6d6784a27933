/* eslint-disable react-hooks/exhaustive-deps */
import React, { useState, useContext, useEffect, Fragment, useRef, forwardRef, useImperativeHandle, useMemo } from 'react';
import { PlusCircleOutlined, QuestionCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import _ from 'lodash';
// import { Filter } from 'wolf-static-cpnt';
import Filter from '../newfilter/Filter';
import FilterSingleWrapper from './FilterSingleWrapper';
import FilterValue from './FilterValue';

import FilterEventFunction from './FilterEventFunction';
import FilterContext from './FilterContext';
import FilterEventProperty from './FilterEventProperty';
import { t } from '../utils/translation';

import FILTER_CONFIG from './FilterConfig';

/**
 * 封装单个过滤组件
 * 当前组件失去焦点时校验数据
 * 当Field、Operator、Value结束编辑时，刺激当前组件获得焦点，以便贸然校验
 */
function FilterSingle({ value, onChange, onDelete }, ref) {
  useImperativeHandle(ref, () => ({
    isValid() {
      if (_.isEmpty(filterRef.current && filterRef.current.getValue())) return true;
      return filterRef.current && filterRef.current.isValid && filterRef.current.isValid(true);
    }
  }));
  const { eventAggregateProperty = {}, action, eventInfo = {}, dateRange = [], eventFilterProperty = {} } = value;
  const { id } = eventInfo;
  const { propertyType, fun, operator } = eventAggregateProperty;
  const { mode, dataProvider } = useContext(FilterContext);

  const _dataProvider = useMemo(() => {
    const pickPd = _.pick(dataProvider, 'getPropertyList');
    pickPd.eventId = id || 0;
    return pickPd;
  }, [id]);

  // 校验器
  const [validator, setValidator] = useState({});

  useEffect(() => {
    if (mode !== 'edit') return;
    // 退出编辑
    setValidator(value.valid());
  }, [action, id, eventAggregateProperty?.value, propertyType, JSON.stringify(dateRange), fun, operator, mode]);

  const filterRef = useRef(null);

  const onChangeFilter = data => {
    value.changePropertyValue(data);
    onChange(value);
  };

  const onAddFilter = () => {
    const filters = filterRef.current.addFilterGroup();
    value.changePropertyValue(filters);
    onChange(value);
  };

  let funValue = '';

  if (fun) {
    funValue = (_.find(FILTER_CONFIG.CONDITIONFUN[eventAggregateProperty?.property?.fieldType || 'INT'], item => {
      return item.value === fun;
    }) || {}).name;
  }

  return (
    <li className={`FilterSingle ${mode}`}>
      <div style={{ display: 'flex' }} hidden={mode !== 'edit' && !value.valid().isValid}>
        {/* <div className={`FilterEventAction ${mode} ${validator?.cusName && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={FILTER_CONFIG.EVENT_ACTION[action]} useTakePlaceWidth>
            <FilterEventAction value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div> */}
        {/* <div className={`FilterField ${mode} ${validator?.id && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={eventInfo.displayName} useTakePlaceWidth>
            <FilterEventFieldSelect value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div> */}
        <div className={`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={value.getValueShow()}>
            <FilterValue value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div>
        <div className={`FilterField ${mode} ${validator?.eventAggregateProperty && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={eventAggregateProperty.property?.fieldName} useTakePlaceWidth>
            <FilterEventProperty value={value} onChange={onChange} eventAggregateProperty={eventAggregateProperty} />
          </FilterSingleWrapper>
        </div>
        {
          <div className={`FilterField ${mode} ${validator?.fun && value.validating ? 'has-error' : ''}`}>
            <FilterSingleWrapper value={funValue} useTakePlaceWidth>
              <FilterEventFunction value={value} onChange={onChange} />
            </FilterSingleWrapper>
          </div>
        }
        {/* <div className={`FilterOperator ${mode} ${validator?.operator && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={value.getOperatorShow()} useTakePlaceWidth>
            <FilterOperator value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div> */}
        {/* <div className={`FilterValue ${mode} ${validator?.value && value.validating ? 'has-error' : ''}`} hidden={value.isValueCanEdit() === false}>
          <FilterSingleWrapper value={value.getValueShow()}>
            <FilterValue value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div> */}
        {/* <div className={`FilterEventSelectTime ${mode} ${validator?.dateRange && value.validating ? 'has-error' : ''}`}>
          <FilterSingleWrapper value={`${timeStr1}~${timeStr2}`}>
            <FilterEventSelectTime value={value} onChange={onChange} />
          </FilterSingleWrapper>
        </div> */}
        {
          mode === 'edit' && <>
            <div className="Ctroller">
              { value.validating && (validator?.action || validator?.id || validator?.eventAggregateProperty || validator?.fun || validator?.operator || validator?.value || validator?.dateRange) && <Tooltip placement="topRight" title={_.head(_.values(validator.message))}>
                <div style={{ marginRight: 5 }}><QuestionCircleOutlined className="Validator" /></div>
              </Tooltip>}
              <span className="handleAdd" onClick={onAddFilter} hidden={filterRef.current && filterRef.current.getFilterCount() >= 5}><PlusCircleOutlined className="add" />{t('cpnt-YwYPb3tJve')}</span>
              <CloseCircleOutlined className="delete" onClick={onDelete} />
            </div>
          </>
        }
      </div>
      <Filter className="innerFilter" dataProvider={_dataProvider} value={eventFilterProperty} onChange={onChangeFilter} mode={mode} ref={filterRef} hideAdd hideInit />
    </li>
  );
}

export default forwardRef(FilterSingle);
