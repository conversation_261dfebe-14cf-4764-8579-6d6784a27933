import { FilterValue, DataProvider } from '../filter/types';

/**
 * 细分/分群接口
 */
export interface Segment {
  id: number | string;
  name: string;
  type?: string;
  [key: string]: any;
}

/**
 * 细分数据提供器接口
 */
export interface SegmentDataProvider extends DataProvider {
  /**
   * @description 获取细分列表
   * @returns 细分列表
   */
  getSegmentList?: () => Segment[] | Promise<Segment[]>;
}

/**
 * Segment组件Props接口
 */
export interface SegmentProps {
  /**
   * @description 细分过滤器的值
   */
  value?: FilterValue;

  /**
   * @description 数据提供器
   */
  dataProvider: SegmentDataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;

  /**
   * @description 组件模式
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否显示初始行
   */
  showInitLine?: boolean;
}

/**
 * Segment组件Ref方法接口
 */
export interface SegmentRef {
  /**
   * @description 校验细分过滤器是否有效
   * @param flag 是否显示验证错误
   * @returns 是否有效
   */
  isValid: (flag?: boolean) => boolean;

  /**
   * @description 获取过滤条件总数
   * @returns 总数
   */
  getFilterCount: () => number;
}
