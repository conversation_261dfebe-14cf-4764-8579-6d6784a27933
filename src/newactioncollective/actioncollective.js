// https://stackoverflow.com/questions/37949981/call-child-method-from-parent
import React, { forwardRef, useImperativeHandle, useRef, useEffect, useState } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import _ from 'lodash';
import ActionCollectiveGroup from './ActionCollectiveGroup';
import EventFilter from '../newevent/Filter';
import HandelGroup from './handlegroup';
import './filter.scss';
import { t } from '../utils/translation';

/**
 * 过滤器组件
 * mode有两种形式，edit|detail，edit模式下可编辑，默认是edit
 * @param {object} 过滤值 value
 * @param {object} 数据提供器 {getPropertyList: (name) => data}
 * @param {function} 响应改变 onChange
 */
const ActionCollective = forwardRef((props, ref) => {
  useImperativeHandle(ref, () => ({
    isValid() {
      // 循环检查所有元素，如果有一个未校验通过，则整体为未通过
      const resultArr = [];

      _.forEach(refObj.current, v => {
        const values = _.values(v);
        _.forEach(_.without(values, null), item => {
          resultArr.push(item.isValid(true));
        });
      });
      return !resultArr.includes(false);
    }
  }));

  const { dataProvider, onChange, mode, value, showInitLine } = props;
  const [show, setShow] = useState(false);
  const refObj = useRef({});

  useEffect(() => {
    if (value.filters.length) {
      setShow(true);
    }
    if (_.isEmpty(value)) {
      if (showInitLine) {
        onChange({
          connector: 'AND',
          filters: [
            {
              connector: 'AND',
              eventGroup: {}
            }
          ]
        });
      } else {
        onChange({
          connector: 'AND',
          filters: []
        });
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  const onChangeConnector = (datas, type) => {
    const result = { ...value, [type]: datas };
    onChange(result);
  };

  const onSubConnectorChange = (datas, type, data) => {
    data[type] = datas;
    onChange({ ...value });
  };

  const count = () => {
    return (value && value.filters && value.filters.length) || 1;
  };

  const handleAddSubItem = (type, index) => {
    const _value = _.cloneDeep(value);
    _value.filters[index][type] = {};
    onChange(_value);
  };

  const onAddItemFilter = type => {
    setShow(true);
    const _value = _.cloneDeep(value);
    _value.filters.push({
      connector: 'AND',
      [type]: {}
    });
    onChange(_value);
  };

  const onChangeItemFilter = (type, v, index, innerValue) => {
    const _value = _.cloneDeep(value);
    if (_.isEmpty(innerValue.filters)) {
      setShow(false);
      _value.filters[index] = _.omit(_value.filters[index], type);
      if (!_value.filters[index].eventGroup && !_value.filters[index].userProperty && !_value.filters[index].userLabel && !_value.filters[index].segment) {
        _value.filters.splice(index, 1);
      }
    } else {
      _value.filters[index][type] = v;
    }
    onChange(_value);
  };

  // const allIndexFilterCount = {};

  // _.forEach(refObj.current, (v, k) => {
  //   let values = _.values(v);
  //   allIndexFilterCount[k] = 0;
  //   _.forEach(values, item => {
  //     if (item) {
  //       allIndexFilterCount[k] += item.getFilterCount();
  //     }
  //   });
  // });

  // useEffect(() => {
  //   let values = _.values(allIndexFilterCount);
  //   let keys = _.keys(allIndexFilterCount);
  //   // 如果values里有为0的值，找到对应的key改变数据
  //   let findIndex = _.findIndex(values, item => item === 0);
  //   if (findIndex !== -1) {
  //     let index = keys[findIndex];
  //     const _value = _.cloneDeep(value);
  //     _value.filters.splice(index, 1);
  //     onChange(_value);
  //   }
  // // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [JSON.stringify(allIndexFilterCount)]);

  const getSubContent = () => {
    return _.map(value.filters, (item, index) => {
      const keys = _.keys(item).slice(1);
      // 整理顺序
      let _keys = [];
      _keys[0] = _.find(keys, key => key === 'eventGroup');
      _keys[1] = _.find(keys, key => key === 'userProperty');
      _keys[2] = _.find(keys, key => key === 'userLabel');
      _keys[3] = _.find(keys, key => key === 'segment');

      _keys = _.without(_keys, false, undefined, null);

      refObj.current[index] = refObj.current[index] || {};
      return <ActionCollectiveGroup
        key={`${index}`}
        connector={item.connector}
        onChangeConnector={res => onSubConnectorChange(res, 'connector', item)}
        filterCount={keys.length}
        inner="inner"
        mode={mode}
      >
        <div>
          {
            _.map(_keys, key => {
              if (key === 'eventGroup') {
                return <EventFilter key="eventGroup" ref={el => refObj.current[index][key] = el} dataProvider={dataProvider} showInitLine value={item[key] || {}} onChange={(v, innerValue) => onChangeItemFilter('eventGroup', v, index, innerValue)} mode={mode} />;
              }
              //  else if (key === 'userProperty') {
              //   return <Filter addButtonText="用户属性" key="userProperty" ref={el => refObj.current[index][key] = el} dataProvider={dataProvider} value={item[key] || {}} onChange={(v, innerValue) => onChangeItemFilter('userProperty', v, index, innerValue)} mode={mode} />;
              // } else if (key === 'userLabel') {
              //   return <Label key="label" ref={el => refObj.current[index][key] = el} dataProvider={dataProvider} value={item[key] || {}} onChange={(v, innerValue) => onChangeItemFilter('userLabel', v, index, innerValue)} mode={mode} />;
              // } else if (key === 'segment') {
              //   return <Segment key="segment" ref={el => refObj.current[index][key] = el} dataProvider={dataProvider} value={item[key] || {}} onChange={(v, innerValue) => onChangeItemFilter('segment', v, index, innerValue)} mode={mode} />;
              // }
            })
          }
          <HandelGroup value={item} key={`${index}`} index={index} onClick={type => handleAddSubItem(type, index)} mode={mode} />
        </div>
      </ActionCollectiveGroup>;
    });
  };

  return (
    <div className="wolf-static-component_filter_FilterGroupPanel_action_collective">
      {
        !showInitLine && _.isEmpty(value?.filters) ? null : <ActionCollectiveGroup
          connector={value.connector}
          onChangeConnector={res => onChangeConnector(res, 'connector')}
          filterCount={count()}
          inner="inner"
          mode={mode}
        >
          {
          getSubContent()
          }
        </ActionCollectiveGroup>
      }

      <div className="action_collective_button_group" hidden={show}>
        <Button type="dashed" icon={<PlusOutlined />} onClick={() => onAddItemFilter('eventGroup')}>{t('cpnt-XrssBb1VqExR')}</Button>
        {/* <Button type="dashed" icon={<PlusOutlined />} onClick={() => onAddItemFilter('userProperty')}>用户属性</Button>
        <Button type="dashed" icon={<PlusOutlined />} onClick={() => onAddItemFilter('userLabel')}>用户标签</Button>
        <Button type="dashed" icon={<PlusOutlined />} onClick={() => onAddItemFilter('segment')}>用户分群</Button> */}
      </div>
    </div>
  );
});

export default ActionCollective;
