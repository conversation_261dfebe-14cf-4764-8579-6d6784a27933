import React, { useRef, useState, useEffect } from 'react';
import { Button, Tooltip } from 'antd';
import _ from 'lodash';
import './index.scss';
import { BoxData, GrabbingBoxProps } from './types';
import { AimOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import { t } from '../utils/translation';
/**
 * 判断是否桌面端
 */
function isPC(): boolean {
  const userAgentInfo = navigator.userAgent;
  const agents = ['Android', 'iPhone', 'SymbianOS', 'Windows Phone', 'iPad', 'iPod'];
  return !agents.some((n) => userAgentInfo.includes(n));
}

/**
 * 阻止移动端浏览器的默认滑动（如下拉刷新与左划回退）
 */
const disableDefaultBehavior = (event: Event) => {
  event.preventDefault();
};

const GrabbingBox = (props: Omit<GrabbingBoxProps, 'mode'>) => {
  const {
    maxScale = 200,
    minScale = 20,
    initScale = 100,
    scaleButtons = true,
    scaleButtonsPosition = 'top right',
    scaleButtonsSpaceX = '70px',
    scaleButtonsSpaceY = '100px',
    scaleStep = 10,
    buttonStyle = {
      position: 'absolute'
    },
    buttonTipProps = {
      overlayClassName: 'grabbing-box-button-tip',
      getPopupContainer: () => document.querySelector('.grabbing-box-container') as HTMLElement,
      trigger: 'hover',
      overlayStyle: { maxWidth: '600px' },
      title: (
        <div>
          {t('cpnt-NTZ2INPgjn6T')}
          <br />
          {t('cpnt-ADeYMrVRXIcT')}
          <br />
          {t('cpnt-blnJosOYMkJU')}
        </div>
      )
    },
    onZoomIn,
    onZoomOut,
    onReset,
    onUpdate,
    children
    // throttleSpan = 50,
  } = props;
  const containerRef = useRef<any>(null);
  const contentBoxRef = useRef<any>(null);
  const [lastTransformData, setLastTransformData] = useState<BoxData>({ scale: 1, translateX: 0, translateY: 0 });
  const lastPositionRef = useRef({ x: 0, y: 0, distance: 0 });
  const [readyToDrag, setReadyToDrag] = useState<boolean>(false);
  const [dragging, setDragging] = useState<boolean>(false);
  const scaling = Math.round(lastTransformData.scale * 100);

  const safeScaleButtonsPosition = {
    top: scaleButtonsPosition.includes('top'),
    bottom: scaleButtonsPosition.includes('bottom'),
    left: scaleButtonsPosition.includes('left'),
    right: scaleButtonsPosition.includes('right')
  };

  // 写一个状态 防止个别键盘重复调用
  const ctrlPressedRef = useRef<boolean>(false);

  useEffect(() => {
    if (maxScale < minScale) {
      throw new Error(`[Props Error]maxScale should greater than minSacle,
          [Props 错误] maxScale应该大于minScale`);
    }
    let _initScale = _.cloneDeep(initScale);
    if (_initScale > maxScale) {
      _initScale = maxScale;
    } else if (_initScale < minScale) {
      _initScale = minScale;
    }
    setLastTransformData({
      ...lastTransformData,
      scale: ensureScaleInRange(_initScale / 100)
    });
  }, []);

  useEffect(() => {
    const container = containerRef.current;
    const _isPC = isPC();
    if (!container) return;
    if (_isPC) {
      container.addEventListener('mousedown', handleMouseDown);
      container.addEventListener('mouseup', handleMouseUp);
      container.addEventListener('mouseleave', handleMouseLeave);
      container.addEventListener('wheel', handleWheel, { passive: false });
      document.addEventListener('keydown', handleKeyDown);
      document.addEventListener('keyup', handleKeyUp);
    }

    return () => {
      container.removeEventListener('mousedown', handleMouseDown);
      container.removeEventListener('mouseup', handleMouseUp);
      container.removeEventListener('mouseleave', handleMouseLeave);
      container.removeEventListener('wheel', handleWheel);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [readyToDrag, lastPositionRef.current]);

  useEffect(() => {
    const element = contentBoxRef.current;
    if (!element) return;
    const { scale, translateX, translateY } = lastTransformData;
    const safeScale = ensureScaleInRange(scale);
    element.style.transform = `matrix(${safeScale}, 0, 0, ${safeScale}, ${translateX}, ${translateY})`;
    element.style.transformOrigin = 'left top'; // 确保缩放中心是元素的中心
    if (onUpdate) onUpdate(lastTransformData);
  }, [lastTransformData, onUpdate]);

  useEffect(() => {
    const container = containerRef.current;
    const _isPC = isPC();
    if (!container || !_isPC) return;

    const handleContextMenu = (event: MouseEvent) => {
      event.preventDefault(); // 禁用鼠标右键菜单
    };

    container.addEventListener('contextmenu', handleContextMenu);

    return () => {
      container.removeEventListener('contextmenu', handleContextMenu);
    };
  }, []);

  // #region todo 通用函数

  /**
   * 确保缩放比例在范围内
   * @param scale
   * @returns
   */
  const ensureScaleInRange = (scale: number) => {
    if (scale * 100 > maxScale) {
      scale = maxScale / 100;
    } else if (scale * 100 < minScale) {
      scale = minScale / 100;
    }
    return scale;
  };

  /**
   * 放大
   */
  const zoomIn = (step?: number) => {
    setLastTransformData((prevData) => {
      const newScale = (prevData.scale * 100 + (step || scaleStep)) / 100;
      const scaleFactor = newScale / prevData.scale;
      const updatedData = {
        ...prevData,
        scale: Math.min(newScale, maxScale / 100),
        translateX: prevData.translateX * scaleFactor,
        translateY: prevData.translateY * scaleFactor
      };
      if (onZoomIn) onZoomIn(updatedData);
      return updatedData;
    });
  };

  /**
   * 缩小
   */
  const zoomOut = (step?: number) => {
    setLastTransformData((prevData) => {
      const newScale = (prevData.scale * 100 - (step || scaleStep)) / 100;
      const scaleFactor = newScale / prevData.scale;
      const updatedData = {
        ...prevData,
        scale: Math.max(newScale, minScale / 100),
        translateX: prevData.translateX * scaleFactor,
        translateY: prevData.translateY * scaleFactor
      };
      if (onZoomOut) onZoomOut(updatedData);
      return updatedData;
    });
  };

  /**
   * 重置
   */
  const reset = () => {
    setLastTransformData({ scale: 1, translateX: 0, translateY: 0 });
    if (onReset) onReset();
  };

  // #endregion 通用函数

  // #region todo PC端事件适配
  const handleMouseDown = (event: any) => {
    containerRef.current?.click();
    setReadyToDrag(true);
    lastPositionRef.current = {
      x: event.clientX,
      y: event.clientY,
      distance: 0
    };
  };

  const handleMouseUp = () => {
    setReadyToDrag(false);
    setDragging(false);
  };

  /**
   * 鼠标滚轮事件、mac触摸板双指滑动
   * 12.6日把默认滚轮事件禁用改为上滑下滑 , 不按ctrl是放大缩小
   * @param event
   */
  const handleWheel = (event: WheelEvent) => {
    const flag = event.ctrlKey;
    if (flag) {
      disableDefaultBehavior(event);
      if (event.deltaY < 0) {
        zoomIn();
      } else {
        zoomOut();
      }
    }
  };

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.ctrlKey && !ctrlPressedRef.current) {
      ctrlPressedRef.current = true;
      handleMouseUp(); // 防止抖动，先执行handleMouseUp
      containerRef.current?.classList.add('ctrl-pressed');
    }
  };

  const handleKeyUp = (event: KeyboardEvent) => {
    if (!event.ctrlKey) {
      ctrlPressedRef.current = false;
      handleMouseUp();
      containerRef.current?.classList.remove('ctrl-pressed');
    }
  };

  // #endregion PC端事件适配

  // 添加鼠标离开容器的处理函数
  const handleMouseLeave = () => {
    setReadyToDrag(false);
    setDragging(false);
    lastPositionRef.current = { x: 0, y: 0, distance: 0 };
  };

  return (
    <div
      ref={containerRef}
      style={{
        width: '100%',
        height: '100%',
        // overflow: 'auto',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}
      className={`grabbing-box-container ${dragging ? 'cursor-grabbing' : 'cursor-grab'}`}
    >
      {scaleButtons && (
        <div
          className={`scale-button-group ${safeScaleButtonsPosition.top ? 'custom-position-top' : ''} ${
            safeScaleButtonsPosition.bottom ? 'custom-position-bottom' : ''
          } ${safeScaleButtonsPosition.left ? 'custom-position-left' : ''} ${
            safeScaleButtonsPosition.right ? 'custom-position-right' : ''
          }`}
          style={
            {
              '--scale-buttons-space-x': scaleButtonsSpaceX,
              '--scale-buttons-space-y': scaleButtonsSpaceY,
              ...buttonStyle
            } as any
          }
        >
          <Button onClick={() => zoomOut(10)} disabled={scaling <= minScale} type="text">
            -
          </Button>
          <span className="scale-button-text">{scaling}%</span>
          <Button onClick={() => zoomIn(10)} disabled={scaling >= maxScale} type="text">
            +
          </Button>
          <span className="scale-button-separator" />
          <Button onClick={reset} type="text">
            <Tooltip title={t('cpnt-zwQo6vfaMzR8')}>
              <AimOutlined />
            </Tooltip>
          </Button>
          <Button type="text">
            <Tooltip {...buttonTipProps}>
              <QuestionCircleOutlined />
            </Tooltip>
          </Button>
        </div>
      )}
      <div
        ref={contentBoxRef}
        className="grabbing-box"
        style={{
          width: 'fit-content',
          height: 'fit-content',
          transformOrigin: 'center center',
          userSelect: 'none',
          transform: 'matrix(1, 0, 0, 1, 0, 0)'
          // boxShadow:
          //   '0px 0px 4px 0px rgba(0, 0, 0, 0.04), 0px 0px 16px 0px rgba(0, 0, 0, 0.04), 0px 0px 24px 0px rgba(0, 0, 0, 0.04)'
        }}
      >
        {children}
      </div>
    </div>
  );
};

const RenderBox = (props: GrabbingBoxProps) => {
  const { grabbing, children, ...rest } = props;
  if (!grabbing && _.isEmpty(rest)) {
    return children;
  }
  return <GrabbingBox {...rest}>{children}</GrabbingBox>;
};

export default RenderBox;
