import { FilterValue, DataProvider } from '../filter/types';

/**
 * 事件接口
 */
export interface Event {
  name: string;
  value: string;
  description?: string;
}

/**
 * 事件数据提供器接口
 */
export interface EventDataProvider extends DataProvider {
  /**
   * @description 获取事件列表
   * @returns 事件列表
   */
  getEventList?: () => Event[] | Promise<Event[]>;

  /**
   * @description 根据事件名称获取事件属性
   * @param eventName 事件名称
   * @returns 事件属性列表
   */
  getEventPropertyList?: (eventName: string) => any[] | Promise<any[]>;
}

/**
 * EventFilter组件Props接口
 */
export interface EventFilterProps {
  /**
   * @description 事件过滤器的值
   */
  value?: FilterValue;

  /**
   * @description 数据提供器
   */
  dataProvider: EventDataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;

  /**
   * @description 组件模式
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否显示初始行
   * @default true
   */
  showInitLine?: boolean;

  /**
   * @description 是否为行为集合模式
   */
  isActionCollection?: boolean;
}

/**
 * EventFilter组件Ref方法接口
 */
export interface EventFilterRef {
  /**
   * @description 校验事件过滤器是否有效
   * @param flag 是否显示验证错误
   * @returns 是否有效
   */
  isValid: (flag?: boolean) => boolean;

  /**
   * @description 获取过滤条件总数
   * @returns 总数
   */
  getFilterCount: () => number;
}
