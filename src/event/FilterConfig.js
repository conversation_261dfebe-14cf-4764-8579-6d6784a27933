import { t } from '../utils/translation';

export default {
  maxFilterCount: 20,
  operatorList: [
    {
      name: t('cpnt-CAzWJHAF5IdX'),
      operator: 'EQ'
    },
    {
      name: t('cpnt-3Y67Jmr39Dcm'),
      operator: 'NE'
    },
    {
      name: t('cpnt-jDZkew5mIrYf'),
      operator: 'GT'
    },
    {
      name: t('cpnt-8dybLQPDLezt'),
      operator: 'GTE'
    },
    {
      name: t('cpnt-3dPcZSPiYfET'),
      operator: 'LT'
    },
    {
      name: t('cpnt-JUioGK4lBWH6'),
      operator: 'LTE'
    },
    {
      name: t('cpnt-FSIQ6RvfpaJU'),
      operator: 'BETWEEN'
    },
    {
      name: t('cpnt-JSR6Ps0v2Z8E'),
      operator: 'ADVANCED_BETWEEN'
    },
    {
      name: t('cpnt-h1BALeLAEQ4j'),
      operator: 'IN'
    },
    {
      name: t('cpnt-kjLnrVkhNX7b'),
      operator: 'NOT_IN'
    },
    {
      name: t('cpnt-ANA4uTPw0TeI'),
      operator: 'IS_NOT_NULL'
    },
    {
      name: t('cpnt-APr5mqgxf5HS'),
      operator: 'IS_NULL'
    },
    {
      name: t('cpnt-7FIzZhMWLQPx'),
      operator: 'ALL'
    },
    {
      name: t('cpnt-n3leUqlSQWdh'),
      operator: 'LIKE'
    },
    {
      name: t('cpnt-mj7tYkII85Vy'),
      operator: 'NOT_LIKE'
    },
    {
      name: t('cpnt-3xB7EIlHeOOB'),
      operator: 'START_WITH'
    },
    {
      name: t('cpnt-GmVsw6Eu8z0L'),
      operator: 'NOT_START_WITH'
    },
    {
      name: t('cpnt-FyLx22FFwMmF'),
      operator: 'END_WITH'
    },
    {
      name: t('cpnt-1ATC9FBcv9Aw'),
      operator: 'NOT_END_WITH'
    },
    {
      name: t('cpnt-zssdZpnkvoa9'),
      operator: 'IS_TRUE'
    },
    {
      name: t('cpnt-1j2VZTJUnM4H'),
      operator: 'IS_FALSE'
    }
  ],
  // typeOperator: {
  //   INT: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   LONG: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   DOUBLE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'IN', 'NOT_IN'],
  //   HIVE_DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATE: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   DATETIME: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   HIVE_TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   TIMESTAMP: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'GT', 'GTE', 'LT', 'LTE', 'BETWEEN', 'ADVANCED_BETWEEN'],
  //   STRING: ['EQ', 'NE', 'IS_NOT_NULL', 'IS_NULL', 'ALL', 'LIKE', 'NOT_LIKE', 'START_WITH', 'NOT_START_WITH', 'END_WITH', 'NOT_END_WITH', 'IN', 'NOT_IN'],
  //   BOOL: ['IS_TRUE', 'IS_FALSE'],
  //   LABEL: ['IN', 'NOT_IN', 'ALL']
  // },
  typeOperator: {
    INT: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    LONG: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    DOUBLE: [
      'EQ',
      'NE',
      'IS_NOT_NULL',
      'IS_NULL',
      'GT',
      'GTE',
      'LT',
      'LTE',
      'BETWEEN'
    ],
    BOOL: ['IS_TRUE', 'IS_FALSE'],
    LABEL: ['IN', 'NOT_IN', 'ALL']
  },
  typeFormatter: {
    DATE: 'YYYY-MM-DD',
    DATETIME: 'YYYY-MM-DD HH:mm:ss',
    TIMESTAMP: 'YYYY-MM-DD HH:mm:ss',
    HIVE_DATE: 'YYYY-MM-DD',
    HIVE_TIMESTAMP: 'YYYY-MM-DD HH:mm:ss'
  },
  connector: [
    {
      name: t('cpnt-ZjczRn5SQrg9'),
      value: 'AND'
    },
    {
      name: t('cpnt-jU1kgZczxvj6'),
      value: 'OR'
    }
  ],
  validator: {
    STRING: {
      option: {
        required: true,
        maxLen: 500
      },
      message: {
        required: t('cpnt-tVpJbKFxQcKL'),
        maxLen: t('cpnt-3A75pGjljoaC')
      }
    },
    INT: {
      option: {
        required: true,
        maxLen: 11,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-tVpJbKFxQcKL'),
        maxLen: t('cpnt-FlJelHfer6iq'),
        regex: t('cpnt-0VwdjEyjdqlZ')
      }
    },
    LONG: {
      option: {
        required: true,
        maxLen: 20,
        regex: '^[+-]?\\d*$'
      },
      message: {
        required: t('cpnt-tVpJbKFxQcKL'),
        maxLen: t('cpnt-g2wH7cXXQiuL'),
        regex: t('cpnt-1gczheMwDX')
      }
    },
    DOUBLE: {
      option: {
        required: true,
        regex: '^[+-]?\\d+(\\.\\d+)?$',
        maxLen: 20
      },
      message: {
        required: t('cpnt-tVpJbKFxQcKL'),
        maxLen: t('cpnt-g2wH7cXXQiuL'),
        regex: t('cpnt-ywxFsCgYp57z')
      }
    },
    DATETIME: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Oq2BpCIIYnlX')
      }
    },
    TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Oq2BpCIIYnlX')
      }
    },
    DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-l7943bhXAr')
      }
    },
    HIVE_TIMESTAMP: {
      option: {
        required: true
      },
      message: {
        required:t('cpnt-Oq2BpCIIYnlX')
      }
    },
    HIVE_DATE: {
      option: {
        required: true
      },
      message: {
        required: t('cpnt-Oq2BpCIIYnlX')
      }
    },
    BOOL: {
      option: {
        required: false
      },
      message: {
        required: t('cpnt-tVpJbKFxQcKL')
      }
    }
  },
  DATE_TYPE_MAP: {
    ABSOLUTE: t('cpnt-og12FL61EeJC'),
    RELATIVE: t('cpnt-gcAC5IOIRpNr')
  },
  relativeTimeObj: {
    0: t('cpnt-Tpdrj7qHKdxJ'),
    1: t('cpnt-zPaZrNr7SCqU'),
    2: t('cpnt-7hg6KrP9bA3c')
  },
  EVENT_ACTION: {
    DONE: t('cpnt-bRaDymyauQUE'),
    NOT_DO: t('cpnt-5j0MCRL4qE2b')
    // DO_SEQ: '依次做过'
  },
  CONDITIONFUN: {
    INT: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    LONG: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    DOUBLE: [
      { name: t('cpnt-HlMfVsj2YbfB'), value: 'COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    TIMES: [
      { name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' },
      { name: t('cpnt-zUq1NRjqfa3A'), value: 'SUM' },
      { name: t('cpnt-Id8PwR7TWGHD'), value: 'AVG' },
      { name: t('cpnt-4xt799uoPhIf'), value: 'MAX' },
      { name: t('cpnt-L7Ct08lJk6Gl'), value: 'MIN' }
    ],
    STRING: [{ name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' }],
    TIMESTAMP: [{ name: t('cpnt-HlMfVsj2YbfB'), value: 'COUNT' }],
    BOOL: [{ name: t('cpnt-O2IrlVmYa6UQ'), value: 'UNIQUE_COUNT' }]
  }
};
