import React, { useState, useContext, useEffect, useRef, Fragment } from 'react';
import { Input, InputNumber } from 'antd';
import _ from 'lodash';
import useDebounce from '../utils/useDebounce';
import FilterContext from './FilterContext';
import { t } from '../utils/translation';

const FilterValueContext = React.createContext();

function NumberBetweenInput(props) {
  let { onChange, fieldValue } = props;
  const inputEl = useRef(null);

  if (!_.isArray(fieldValue)) {
    fieldValue = [];
  }
  return (
    <>
      <InputNumber
        style={{ width: 100, marginRight: '5px' }}
        placeholder={t('cpnt-X4g308bbEJJu')}
        value={fieldValue[0]}
        ref={inputEl}
        onChange={value => !isNaN(value) && onChange([value, fieldValue[1]])}
      />
      {t('cpnt-Bd7wiegSp91n')}
      <InputNumber
        style={{ width: 100, marginLeft: '5px' }}
        placeholder={t('cpnt-PFwYP08siK29')}
        value={fieldValue[1]}
        onChange={value => !isNaN(value) && onChange([fieldValue[0], value])}
      />
    </>
  );
}

// const longToMoment = fv => {
//   return _.isArray(fv)
//     ? fv.map(v => dayjs(v))
//     : undefined;
// };

/**
 * 范围日历输入框
 */
// function DateBetweenInput(props) {
//   let { fieldValue, fieldType, onChange } = props;
//   const [value, setValue] = useState(longToMoment(fieldValue));
//   const showTime = !!(fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP');
//   const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
//   let unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
//   const onValueChange = m => {
//     setValue(m);
//     onChange(m && m[0] && m[1] && [m[0].startOf(unit).valueOf(), m[1].startOf(unit).valueOf()]);
//   };

//   // const onValueOk = m => {
//   //   onChange(m && m[0] && m[1] && [m[0].valueOf(), m[1].valueOf()]);
//   // };

//   return (
//     <RangePicker
//       allowClear={false}
//       showTime={showTime}
//       format={format}
//       placeholder={['开始时间', '结束时间']}
//       // onOk={onValueOk}
//       onChange={onValueChange}
//       value={value}
//     />
//   );
// }

function TwoInput(props) {
  const { fieldType, operator, onChange, fieldValue, items } = props;
  return (
    <NumberBetweenInput
      fieldType={fieldType}
      operator={operator}
      fieldValue={fieldValue}
      items={items}
      onChange={onChange}
    />
  );
}

// function DateInput(props) {
//   let { fieldType, onChange } = props;

//   const showTime = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? { format: 'HH:mm:ss' } : null;
//   const format = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD';
//   let unit = (fieldType === 'DATETIME' || fieldType === 'TIMESTAMP' || fieldType === 'HIVE_TIMESTAMP') ? 'second' : 'day';
//   const onValueChange = m => {
//     onChange(m.startOf(unit).valueOf());
//   };

//   return (
//     <DatePicker
//       placeholder="请输入日期"
//       showTime={showTime}
//       format={format}
//       // allowClear
//       allowClear={false}
//       value={props.fieldValue && dayjs(props.fieldValue)}
//       // getCalendarContainer={triggerNode => triggerNode.parentNode}
//       onChange={onValueChange}
//     />
//   );
// }

/**
 * 返回
 * @param {FilterModel} value
 * @param {function} onChange
 */
function SingleInput(props) {
  const { fieldValue, onChange } = props;
  const inputEl = useRef(null);
  return <InputNumber
    style={{ width: 100, marginRight: '5px' }}
    placeholder={t('cpnt-g4qOgzDPFkDP')}
    value={fieldValue}
    ref={inputEl}
    min={0}
    onChange={onChange}
  />;
}

/**
 * *****************************************
 * 主组件
 * *****************************************
 */
export default function FilterValue({ value, onChange }) {
  const { logProvider } = useContext(FilterContext);
  const { eventAggregateProperty = {} } = value;
  const [items] = useState([]);
  const [log] = useState(logProvider.getLogger('FilterValue'));
  const [, setMenuVisible] = useState(false);
  const [filterValueContext] = useState({
    setMenuVisible
  });

  const { operator, propertyType } = eventAggregateProperty;

  let fieldType = 'STRING';

  if (propertyType === 'TIMES') {
    fieldType = 'INT';
  } else {
    fieldType = eventAggregateProperty?.property?.fieldType || 'STRING';
  }
  const [fieldValue, setFieldValue] = useState(eventAggregateProperty.value);
  const debounceFieldValue = useDebounce(fieldValue, 500);

  useEffect(() => {
    setFieldValue(eventAggregateProperty.value);
  }, [eventAggregateProperty.value]);

  useEffect(() => {
    log.debug('debounceFieldValue changed call onChange', debounceFieldValue);
    if (debounceFieldValue === null || debounceFieldValue === undefined) return;
    onChange(debounceFieldValue);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceFieldValue]);

  const onChangeFieldValue = v => {
    log.debug('onChangeFieldValue', v, JSON.stringify(value));
    value.changeValue(v);
    setFieldValue(v);
  };

  /**
   * 主方法
   * 返回渲染组件
   */
  const filterValue = () => {
    switch (operator) {
      case 'EQ':
      case 'NE':
      case 'GT':
      case 'GTE':
      case 'LT':
      case 'LTE':
      case 'LIKE':
      case 'NOT_LIKE':
      case 'START_WITH':
      case 'NOT_START_WITH':
      case 'END_WITH':
      case 'NOT_END_WITH':
        return (
          <SingleInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'BETWEEN':
        return (
          <TwoInput
            fieldType={fieldType}
            operator={operator}
            fieldValue={fieldValue}
            items={items}
            onChange={onChangeFieldValue}
          />
        );
      case 'IS_NOT_NULL':
      case 'IS_NULL':
      case 'IS_TRUE':
      case 'IS_FALSE':
      case 'ALL':
        return <span />;

      default:
        return <Input placeholder={t('cpnt-g4qOgzDPFkDP')} disabled={!operator} />;
    }
  };

  return (
    <FilterValueContext.Provider value={filterValueContext}>
      <div>{filterValue()}</div>
    </FilterValueContext.Provider>
  );
}
