import React, { Fragment, useState, useEffect, useContext } from 'react';
import _ from 'lodash';
import dayjs from 'dayjs';
import FlowCanvasNode from './FlowCanvasNode';
// import FlowNodeDragContext from './FlowNodeDragContext';
import { DraggableContext } from './DraggableContext';
import FlowCanvasContext from './FlowCanvasContext';
import InsertSquare from './InsertSquare';
import { t } from '../utils/translation';

// function CanvasNode({ model, mode, onMouseLeave, onMouseEnter, onBlur, onFocus }) {
//   const [inCounts, setInCounts] = useState(model.inCounts);
//   const [hoverCss, setHoverCess] = useState({
//     node: '',
//     inCounts: ''
//   });

//   useEffect(() => {
//     if (mode !== 'detail') return;
//     setInCounts(model.inCounts || 0);
//     if (model.inCounts !== 0) {
//       setHoverCess({
//         node: 'animated',
//         inCounts: 'animated bounceIn'
//       });
//       setTimeout(() => {
//         setHoverCess({
//           node: '',
//           inCounts: ''
//         });
//       }, 1000);
//     }
//   }, [model.inCounts, mode]);

//   const getNodeStyle = () => {
//     let style = '';
//     if (model.isEditable()) {
//       style += ' editable ';
//       if (!model.detail || _.isEmpty(model.detail) || model.detail.hasData !== true) {
//         style += ' noData ';
//       }
//       if (model.detail && !model.detail?.isValid && model.detail?.isDirty) {
//         style += ' invalid ';
//       }
//     }
//     return style;
//   };

//   const style = getNodeStyle();
//   // const onClick = () => {
//   //   if (mode === 'edit' && model.isEditable()) {
//   //     return onEditNode(model.nodeId);
//   //   }
//   // };

//   return (
//     <div onMouseEnter={onMouseEnter} onMouseLeave={onMouseLeave} onBlur={onBlur} onFocus={onFocus} className={`${hoverCss.node}`}>
//       <FlowNode value={model} mode={mode} style={style}>
//         {mode === 'detail' && <div className={`${hoverCss.inCounts}`}>{`${inCounts}${t('cpnt-irFcQ8TzlM7I')}`}</div>}
//       </FlowNode>
//     </div>
//   );
// }

const timeObj = {
  MINUTE: t('cpnt-ptdKTgqpOMyg'),
  DAY: t('cpnt-CijH0QCNEkLA'),
  HOUR: t('cpnt-axF4mdh3uvX7')
};

export default function FLowCanvasNodeUnit({ x, y, unitRect, containerRect, insertRect, model, branchName, maxY }) {
  const { context: { dragging }, dragDrop } = useContext(DraggableContext);
  const { onDropNode, connectingNodeAction, mode, onClickNode, onUpdateNode, connectingNode, onNodeHover, editingNode, dataProvider } = useContext(FlowCanvasContext);

  const [dropable, setDropable] = useState(false);
  const [hover, setHover] = useState(false);
  const [dropHover, setDropHover] = useState(false);

  const droppable = (x === 0 && y === 0 && model === null) || (x === 0 && y === maxY && model === null);

  const canDrop = () => {
    if ((x === 0 && y === 0 && dragging?.joinType === 'ENTRY' && !model) || (x === 0 && y === maxY && dragging?.joinType === 'ENTRY' && !model)) {
      return true;
    }
    return false;
  };

  useEffect(() => {
    if (!dragging) {
      setDropHover(false);
    }
  }, [dragging]);

  useEffect(() => {
    setDropable(mode === 'edit');
  }, [mode]);

  const onDrop = e => {
    if (!dropable || !dragging) return;
    e.stopPropagation();
    e.preventDefault();
    dragDrop({ type: 'drop' });
    if (canDrop()) onDropNode(x, y, dragging);
  };

  const onDragEnter = () => {
    // 如果已经有节点，由节点判断可不可以drop
    if (!dropable || model) return;
    dragDrop({ type: 'dragEnter', droppable: canDrop() });
    if (!dropHover) setDropHover(true);
  };

  const onDragLeave = () => {
    // 如果已经有节点，由节点判断可不可以drop
    if (!dropable || model) return;
    dragDrop({ type: 'dragLeave', droppable: null });
    if (dropHover) setDropHover(false);
  };

  const onMouseDown = () => {
    connectingNodeAction({ type: 'mouseDown', pos: { x, y } });
  };

  const onMouseUp = () => {
    connectingNodeAction({ type: 'mouseUp', pos: { x, y } });
  };

  const onMouseEnter = () => {
    if (!hover && !connectingNode?.moving) setHover(true);
    connectingNodeAction({ type: 'mouseMove', pos: { x, y } });
    onNodeHover(model, unitRect);
  };

  const onMouseLeave = () => {
    if (hover) setHover(false);
  };

  const TimeV2Display = detail => {
    const { day, hour, minute } = detail;
    let resTime = '';
    if (day === 0 && hour > 0 && minute > 0) {
      resTime = `${detail.hour}${timeObj.HOUR}${detail.minute}${timeObj.MINUTE}`;
    } else if (day === 0 && hour === 0 && minute > 0) {
      resTime = `${detail.minute}${timeObj.MINUTE}`;
    } else if (day === 0 && hour > 0 && minute === 0) {
      resTime = `${detail.hour}${timeObj.HOUR}`;
    } else if (day > 0 && hour > 0 && minute === 0) {
      resTime = `${detail.day}${timeObj.DAY}${detail.hour}${timeObj.HOUR}`;
    } else if (day > 0 && hour === 0 && minute === 0) {
      resTime = `${detail.day}${timeObj.DAY}`;
    } else {
      resTime = `${detail.day}${timeObj.DAY}${detail.hour}${timeObj.HOUR}${detail.minute}${timeObj.MINUTE}`;
    }
    return resTime;
  };

  const [atTimeData, setAtTimeData] = useState('');

  if (dataProvider) {
    if (model && model.busiType === 'AtTimeNode' && model.detail) {
      const getAtTime = async () => {
        const resultData = await model.getAtTime(dataProvider && dataProvider.getAtTimeNodesData);
        setAtTimeData(resultData);
      };
      getAtTime();
    }
  }

  // 设置标签节点的文本显示
  const tagText = [];
  let top = 8;
  if (model && model.busiType === 'MarkTagNode' && model.detail) {
    _.forEach(model.detail.nodeLabelList, (item, index) => {
      // if(index !== model.detail.nodeLabelList.length -1){
      //   tagText += `${item.nodeLabel}：${item.value}\n`
      // }else{
      //   tagText += `${item.nodeLabel}：${item.value}`
      // }
      tagText.push(<div key={index}>{`${item.nodeLabel}：${item.value}`}</div>);
    });
    if (model.detail.nodeLabelList.length === 2) {
      top = 5;
    } else if (model.detail.nodeLabelList.length === 3) {
      top = -2;
    } else if (model.detail.nodeLabelList.length === 4) {
      top = -12;
    } else if (model.detail.nodeLabelList.length === 5) {
      top = -21;
    }
  }

  return (
    <>
      <InsertSquare insertRect={insertRect} insertBeforeNode={model} />
      <div
        className={`node ${mode} ${droppable ? 'droppable' : ''} ${
          dropHover && droppable ? 'hover' : ''
        }`}
        style={{ ...containerRect }}
        onDropCapture={onDrop}
        onDragEnter={onDragEnter}
        onDragLeave={onDragLeave}
        onMouseDown={onMouseDown}
        onMouseUp={onMouseUp}
        onMouseEnter={onMouseEnter}
      >
        {model && branchName && !dragging && !editingNode && (
          <div
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onFocus={onMouseEnter}
            onBlur={onMouseLeave}
            className="branchName"
          >
            {branchName}
          </div>
        )}
        {model && tagText && !dragging && !editingNode && (
          <div
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onFocus={onMouseEnter}
            onBlur={onMouseLeave}
            className="tagRight"
            style={{ top }}
          >
            {tagText}
          </div>
        )}
        {model && (
          <FlowCanvasNode
            model={model}
            mode={mode}
            onMouseEnter={onMouseEnter}
            onMouseLeave={onMouseLeave}
            onFocus={onMouseEnter}
            onBlur={onMouseLeave}
            onUpdateNode={onUpdateNode}
            onClickNode={onClickNode}
          />
        )}
        {model && model.busiType === 'WaitTimerNode' && model.detail && !dragging && !editingNode && (
          <div
            // onMouseEnter={onMouseEnter}
            // onMouseLeave={onMouseLeave}
            // onFocus={onMouseEnter}
            // onBlur={onMouseLeave}
            className="timerRight"
          >
            {model.detail.time} {timeObj[model.detail.timeUnit]}
          </div>
        )}
        {model && model.busiType === 'WaitTimerV2Node' && model.detail && !dragging && !editingNode && (
          <div
            // onMouseEnter={onMouseEnter}
            // onMouseLeave={onMouseLeave}
            // onFocus={onMouseEnter}
            // onBlur={onMouseLeave}
            className="timerRight"
          >
            {TimeV2Display(model.detail)}
          </div>
        )}
        {model && model.busiType === 'AtTimeNode' && model.detail && !dragging && !editingNode && (
          <div
            className="timerRight"
          >
            {atTimeData && dayjs(atTimeData).format('YYYY-MM-DD HH:mm')}
          </div>
        )}
      </div>
    </>
  );
}
