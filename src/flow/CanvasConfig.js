import { t } from '../utils/translation';

export default {
  initX: 1,
  initY: 1,
  maxX: 100,
  maxY: 100,
  showAuxLine: true,
  unitHeight: 136,
  unitWidth: 136,
  iconWidth: 48,
  iconHeight: 48,
  nodeContainerWidth: 64,
  nodeContainerHeight: 64,
  helperNodes: [
    {
      name: t('cpnt-WHFAXCIKmdWp'),
      busiType: 'ExitHelperNode',
      color: '#1EC78B',
      icon: '#iconyonghuzhongxin-1',
      shape: 'CIRCLE',
      joinType: 'END'
    },
    {
      name: '',
      busiType: 'JoinHelperNode',
      color: '#FCA400',
      icon: '#iconicon_xiaochengxu',
      shape: 'LIT_DIAMOND',
      joinType: 'JOIN'
    },
    {
      name: t('cpnt-eQYOd1wKX90O'),
      displayName: t('cpnt-eQYOd1wKX90O'),
      busiType: 'JoinNode',
      color: '#FCA400',
      icon: '#icongaibanxianxingtubiao-',
      shape: 'DIAMOND',
      joinType: 'FUNCTION'
    }
  ]
};
