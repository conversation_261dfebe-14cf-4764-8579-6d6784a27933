import { GrabbingBoxProps } from '../grabbing/types';

/**
 * 流程节点接口
 */
export interface FlowNode {
  id: string;
  name: string;
  type?: string;
  displayName?: string;
  displayInBox?: boolean;
  children?: FlowNode[];
  [key: string]: any;
}

/**
 * 流程数据提供器接口
 */
export interface FlowDataProvider {
  /**
   * @description 获取流程节点列表
   * @returns 流程节点数组
   */
  getFlowBoxNodes: () => FlowNode[] | Promise<FlowNode[]>;

  /**
   * @description 获取时间节点数据（可选）
   * @returns 时间节点数据
   */
  getAtTimeNodesData?: () => Record<string, number> | Promise<Record<string, number>>;
}

/**
 * 画布配置接口
 */
export interface CanvasConfig {
  helperNodes?: FlowNode[];
  gridSize?: number;
  snapToGrid?: boolean;
  [key: string]: any;
}

/**
 * FlowEditor组件Props接口
 */
export interface FlowEditorProps {
  /**
   * @description 数据提供器，需包含getFlowBoxNodes方法
   */
  dataProvider: FlowDataProvider;

  /**
   * @description 当前流程数据
   */
  value?: any;

  /**
   * @description 流程数据改变时的回调
   */
  onChange?: (value: any) => void;

  /**
   * @description 画布模式
   */
  mode?: 'edit' | 'detail' | 'preview' | 'template';

  /**
   * @description 编辑节点时的回调
   */
  onEditNode?: (value?: any) => void;

  /**
   * @description 点击节点时的回调
   */
  onClickNode?: (value?: any) => void;

  /**
   * @description 是否开启调试模式
   */
  debug?: boolean;

  /**
   * @description 画布配置
   */
  canvasConfig?: CanvasConfig;

  /**
   * @description 是否开启缩放功能
   */
  grabbing?: boolean | GrabbingBoxProps;
}

/**
 * FlowCanvas组件Props接口
 */
export interface FlowCanvasProps {
  /**
   * @description 数据提供器
   */
  dataProvider: FlowDataProvider;

  /**
   * @description 当前流程数据
   */
  value?: any;

  /**
   * @description 流程数据改变时的回调
   */
  onChange?: (value: any) => void;

  /**
   * @description 画布模式
   */
  mode?: 'edit' | 'detail' | 'preview' | 'template';

  /**
   * @description 编辑节点时的回调
   */
  onEditNode?: (value?: any) => void;

  /**
   * @description 点击节点时的回调
   */
  onClickNode?: (value?: any) => void;

  /**
   * @description 是否开启调试模式
   */
  debug?: boolean;

  /**
   * @description 是否开启缩放功能
   */
  grabbing?: boolean | GrabbingBoxProps;
}

/**
 * FlowNodeBox组件Props接口
 */
export interface FlowNodeBoxProps {
  /**
   * @description 组件模式
   */
  mode?: 'edit' | 'detail' | 'preview' | 'template';

  /**
   * @description 节点数据列表
   */
  boxNodes: FlowNode[];
}
