import React from 'react';
import { Collapse } from 'antd';
// import DragableNode from './DragableNode';
import Draggable from './Draggable';
import FlowNode from './FlowNode';
import './FlowNodeBox.scss';
import { t } from '../utils/translation';

const { Panel } = Collapse;

export default function FlowNodeBox({ mode, boxNodes }) {
  const draggable = mode === 'edit' || mode === 'template';

  return (
    <div className="wolf-static-cpnt FlowNodeBox">
      <div className="boxHeder">{t('cpnt-ChfAVnS6ZI4P')}</div>
      <Collapse defaultActiveKey={[0, 1, 2, 3, 4, 5]} expandIconPosition="right">
        {boxNodes.map((v, i) => (
          <Panel header={v.displayName} key={i}>
            {v.children.filter(vv => vv.displayInBox).map((vv, ii) => (
              <div key={ii}>
                <Draggable draggable={draggable} droppable={null} obj={vv} draggingHoverCss="">
                  <FlowNode value={vv} />
                </Draggable>
              </div>
            ))}
          </Panel>
        ))}
      </Collapse>
    </div>
  );
}
