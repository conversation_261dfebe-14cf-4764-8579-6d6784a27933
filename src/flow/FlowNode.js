import React, { useState, useRef, useEffect } from 'react';
import { Form, Input } from 'antd';
import './FlowNode.scss';

const EditableCellName = (props) => {
  const [editing, setEditing] = useState(false);
  const inputRef = useRef(null);
  const [form] = Form.useForm();

  useEffect(() => {
    if (editing) {
      inputRef.current && inputRef.current.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
  };

  const save = async () => {
    try {
      const values = await form.validateFields();
      toggleEdit();
      props.onSave(values);
    } catch (error) {
      console.error("🚀 ~ save ~ error:", error)
    }
  };

  const renderCell = () => {
    return editing ? (
      <Form form={form} component={false} initialValues={{ name: props.value.displayName }}>
        <Form.Item
          style={{ margin: 0 }}
          name="name"
          initialValue={props.value.displayName}
        >
          <Input
            size="small"
            maxLength={15}
            ref={inputRef}
            onPressEnter={save}
            onBlur={save}
          />
        </Form.Item>
      </Form>
    ) : (
      <div
        className="editable-cell-value-wrap-name"
        onClick={toggleEdit}
      >
        {props.value.displayName}
      </div>
    );
  };

  return (
    <div onClick={e => e.stopPropagation()}>
      {renderCell()}
    </div>
  );
};

export default function FlowNode({ value, children, mode, style, onClick, onUpdateNode }) {
  const onsave = values => {
    if (values.name && values.name.length <= 15 && values.name !== value.displayName) {
      value.displayName = values.name;
      onUpdateNode(value);
    }
  };
  const bg = (mode !== 'edit' && mode !== 'template' || !style || style.indexOf('noData') < 0) ? {
    backgroundColor: value.color
  } : {};
  return (
    <div className="wolf-static-cpnt FlowNode" onClick={onClick}>
      <div className={`container ${mode} ${style || ''}`}>
        <div className={`icon ${value.shape}`} style={{ ...bg }}>
          <svg className="wolf-icon" aria-hidden="true">
            <use xlinkHref={value.icon} />
          </svg>
        </div>
        {mode === 'edit' ? <EditableCellName onSave={onsave} value={value} />
          : <div className="name" title={value.displayName}>{value.displayName}</div>}
        <div hidden={value.nodeId === undefined} className="id" title={value.nodeId}>ID：{value.nodeId}</div>
        <div className="userCount">{children}</div>
      </div>
    </div>
  );
}
