import React, { useState, useReducer, useEffect } from 'react';
import { Layout, Button } from 'antd';
import debounce from 'lodash/debounce';
import _ from 'lodash';
import Log from '../utils/log';
import './FlowEditor.scss';
import FlowCanvas from './FlowCanvas';
import FlowNodeBox from './FlowNodeBox';
import FlowHistoryHolder from './FlowHistoryHolder';
import FlowEditorContext from './FlowEditorContext';
import CanvasConfig from './CanvasConfig';
import {
  DraggableContext,
  DraggableContextFactory,
  DragDropReducer
} from './DraggableContext';
import { t } from '../utils/translation';

/** @typedef {import('./types').FlowEditorProps} FlowEditorProps */
/** @typedef {import('./types').FlowDataProvider} FlowDataProvider */
/** @typedef {import('./types').CanvasConfig} CanvasConfig */

const { Content, Sider } = Layout;

const history = new FlowHistoryHolder();

/**
 * FlowEditor流程编辑器组件
 * @param {FlowEditorProps} props 组件属性
 * @returns {React.Component}
 */
export default function FlowEditor(props) {
  /**
   * dataProvider {
   *  getFlowBoxNodes()
   * }
   */
  const {
    dataProvider,
    onChange,
    mode,
    value,
    onEditNode,
    onClickNode,
    debug,
    canvasConfig,
    grabbing
  } = props;
  const [log] = useState(Log.getLogger('FlowEditor'));
  const [dragdropNodeContext, dragDrop] = useReducer(
    DragDropReducer,
    DraggableContextFactory.build()
  );
  const [boxNodes, setBoxNodes] = useState([]);
  // const [history] = useState(() => new FlowHistoryHolder());

  const [func] = useState(() => {
    return {
      onRedu: () => {
        const reduValue = history.redu();
        reduValue.type = 'onRedu';
        if (reduValue) onChange(reduValue);
      },

      onUndu: () => {
        const unduValue = history.undu();
        unduValue.type = 'onUndu';
        if (unduValue) onChange(unduValue);
      }
    };
  });

  const onValueChange = v => {
    if (mode !== 'edit' && mode !== 'template') return;
    if (_.isEqual(v, value) && history.currentIndex !== -1) return;
    history.push(v);
    onChange(v);
  };

  // useEffect(() => {
  //   debugger;
  //   history.push(value);
  // }, [value]);

  const dbDragDrop = debounce(dragDrop, 300);

  const debouceDragDrop = args => {
    if (args.type === 'dragEnter' || args.type === 'dragLeave') {
      dbDragDrop(args);
    } else {
      dragDrop(args);
    }
  };

  const [context] = useState({
    dataProvider,
    logProvider: Log,
    canvasConfig: canvasConfig || CanvasConfig
  });

  useEffect(() => {
    const fetchData = async () => {
      const data = await dataProvider.getFlowBoxNodes();
      setBoxNodes(data);
      context.canvasConfig.helperNodes = data.flatMap(v => v.children);
    };
    fetchData();
  }, [dataProvider, context.canvasConfig]);

  // 父组件改变，设置当前值
  // useEffect(() => {
  //   log.debug('props.dataProvider changed');
  //   setContext({
  //     dataProvider,
  //     logProvider: Log,
  //     canvasConfig: context.canvasConfig
  //   });
  // }, [dataProvider, context.canvasConfig, log]);

  const onDrop = () => {
    log.debug('drop');
    dragDrop({ type: 'dragEnd' });
  };

  log.debug('before render', JSON.stringify(dragdropNodeContext));

  return (
    <div className="wolf-static-cpnt FlowEditor">
      <FlowEditorContext.Provider value={context}>
        <DraggableContext.Provider
          value={{ context: dragdropNodeContext, dragDrop: debouceDragDrop }}
        >
          <Layout
            // style={{
            //   cursor: dragdropNodeContext?.dragging ? 'grabbing' : 'auto'
            // }}
            onDropCapture={onDrop}
            onDragOver={e => e.preventDefault()}
          >
            <Sider width={300} theme="light">
              <FlowNodeBox mode={mode} boxNodes={boxNodes} />
            </Sider>
            <Content>
              <div className="canvasTitle">
                {t('cpnt-0UYMlZQSfE1e')}
                <div className="historyCtrl">
                  <Button
                    disabled={!history.canUndu()}
                    onClick={debounce(func.onUndu, 500)}
                    type="link"
                  >
                    <svg
                      style={{ width: 16, height: 16 }}
                      className="wolf-icon"
                      aria-hidden="true"
                    >
                      <use xlinkHref="#iconchexiao-2copy" />
                    </svg>
                    <span style={{ marginLeft: 6 }}>{t('cpnt-wBuk1F1LxPhJ')}</span>
                  </Button>
                  <Button
                    disabled={!history.canRedu()}
                    onClick={debounce(func.onRedu, 500)}
                    type="link"
                  >
                    <svg
                      style={{ width: 16, height: 16 }}
                      className="wolf-icon"
                      aria-hidden="true"
                    >
                      <use xlinkHref="#iconchexiao-2" />
                    </svg>
                    <span style={{ marginLeft: 6 }}>{t('cpnt-nbx2J6BqraJ1')}</span>
                  </Button>
                  {/* <Button
                    shape="circle"
                    icon="caret-left"
                    disabled={!history.canUndu()}
                    onClick={debounce(func.onUndu, 500)}
                  />
                  <Button
                    shape="circle"
                    icon="caret-right"
                    disabled={!history.canRedu()}
                    onClick={debounce(func.onRedu, 500)}
                  /> */}
                </div>
              </div>
              <FlowCanvas
                onChange={onValueChange}
                dataProvider={dataProvider}
                value={value}
                mode={mode}
                onEditNode={onEditNode}
                onClickNode={onClickNode}
                debug={debug}
                grabbing={grabbing}
              />
            </Content>
          </Layout>
        </DraggableContext.Provider>
      </FlowEditorContext.Provider>
    </div>
  );
}
