import _ from 'lodash';
import FlowNodeModelCanvas from '../model/FlowNodeModelCanvas';
import FlowNodeModel from '../model/FlowNodeModel';
import Dag from './Dag';
import Log from '../../utils/log';

const { max } = Math;

const log = Log.getLogger('FlowCanvasCtrl');
export default class FlowCanvasCtrl {
  #maxNodeId = 0;

  #nodeTop = 30;

  #flows = [];

  constructor({ initX, initY, maxX, maxY, showAuxLine, unitHeight, unitWidth, iconWidth, iconHeight, nodeContainerWidth, nodeContainerHeight, helperNodes }) {
    log.debug('constructor');
    _.forEach(
      { initX, initY, maxX, maxY, showAuxLine, unitHeight, unitWidth, iconWidth, iconHeight, nodeContainerWidth, nodeContainerHeight },
      (v, k) => (this[k] = v)
    );
    this.helperNodeMap = helperNodes.reduce((a, b) => {
      a[b.busiType] = b;
      return a;
    }, {});
    this.dag = new Dag([]);
  }

  getFlows() {
    return this.#flows;
  }

  setFlows(flows, fixable) {
    if (flows === this.#flows) return;
    const fromJson = () => {
      if (_.isEmpty(flows)) return [];
      return flows.map(v => new FlowNodeModelCanvas(v));
    };
    log.debug('redraw value');
    this.dag = new Dag(fromJson(flows));
    this.#flows = flows;
    if (fixable) {
      const fixed = this.__fixFlows();
      if (fixed) this.__changeFlows();
      return fixed;
    }
  }

  getMaxX() {
    return this.dag.reduce((a, b) => max(a, b.x), 0);
  }

  getMaxY() {
    return this.dag.reduce((a, b) => max(a, b.y), 0);
  }

  getWidth() {
    return max((this.getMaxX() + 1) * this.unitWidth, this.initX * this.unitWidth);
  }

  getHeight() {
    let maxY;
    if (this.dag.isEmpty()) {
      maxY = 1;
    } else if (this.getMaxY() === 0) {
      maxY = 2;
    } else {
      maxY = this.getMaxY() + 2;
    }
    return max(maxY * this.unitHeight, this.initY * this.unitHeight);
  }

  getGrideX() {
    return this.getWidth() / this.unitWidth + 1;
  }

  getGrideY() {
    return this.getHeight() / this.unitHeight + 1;
  }

  getBg() {
    return { width: this.getWidth() + this.unitWidth, height: this.getHeight() + this.unitHeight };
  }

  getAuxLine(lineFactory) {
    const hLine = i => {
      return lineFactory(i * this.unitWidth, 0, i * this.unitWidth, this.getHeight());
    };

    const vLine = i => {
      return lineFactory(0, i * this.unitHeight, this.getWidth(), i * this.unitHeight);
    };

    return _.range(0, this.getGrideX())
      .map(hLine)
      .concat(
        _.range(0, this.getGrideY()).map(vLine)
      );
  }

  getAuxRects(rectFatory) {
    const { unitWidth, unitHeight } = this;

    const getRect = (x, y) => {
      const rect = {
        left: x * unitWidth,
        top: y * unitHeight,
        width: unitWidth,
        height: unitHeight
      };

      return rectFatory(x, y, rect);
    };

    return _.range(0, this.getGrideX())
      .flatMap(x => {
        return _.range(0, this.getGrideY()).map(y => [x, y]);
      })
      .map(([x, y]) => getRect(x, y));
  }

  getNodeContainerRect(nodeId) {
    const node = this.dag.getNodeByNodeId(nodeId);
    const { unitWidth, unitHeight, nodeContainerWidth, nodeContainerHeight, iconHeight } = this;
    const { x, y } = node;
    return {
      left: x * unitWidth + (unitWidth - nodeContainerWidth) / 2,
      top:
        y * unitHeight + (unitHeight - nodeContainerHeight) / 2 + (nodeContainerHeight - iconHeight) / 2,
      width: nodeContainerWidth,
      height: nodeContainerHeight
    };
  }

  /**
   * 获得节点容器
   * @param {function} nodeFactory 节点容器的构造方法
   */
  getNodeUnit(nodeFactory) {
    // const fixRet = this.fixFlows();
    // // 如果修复了，那么不需要刷新组件直接返回，等着下次刷新
    // if (fixRet) return '';
    this.dag.check();
    const { unitWidth, unitHeight, nodeContainerWidth, nodeContainerHeight, iconWidth, iconHeight } = this;
    const node = (x, y) => {
      const model = this.dag.getNodeByXy(x, y);
      const unitRect = {
        left: x * unitWidth,
        top: y * unitHeight + this.#nodeTop,
        width: unitWidth,
        height: unitHeight
      };

      const containerRect = {
        left: x * unitWidth + (unitWidth - nodeContainerWidth) / 2,
        top: y * unitHeight + (unitHeight - nodeContainerHeight) / 2 + (nodeContainerHeight - iconHeight) / 2,
        width: nodeContainerWidth,
        height: nodeContainerHeight
      };

      const insertRect = {
        left: x * unitWidth - iconWidth / 2,
        top: y * unitHeight + (unitHeight - nodeContainerHeight) / 2 + (nodeContainerHeight - iconHeight) / 2,
        width: iconWidth,
        height: iconHeight
      };

      let branchName = '';
      if (model !== null) {
        const branch = this.__getNodeFatherBranch(model.fatherIds, model.branchIndex);
        if (branch) {
          branchName = branch.branchName;
          if (branch.branchPercent) {
            branchName = `${branch.branchPercent}%_${branch.branchName}`;
          }
        }
      }
      return nodeFactory({ x, y, containerRect, unitRect, insertRect, model, branchName, maxY: this.getMaxY() + 1 });
    };
    // 原点上没有东西
    // if (!this.dag.getNodeByXy(0, 0)) {
    //   return [node(0, 0)];
    // }
    // 判断当前是否有node，不一定是原点，可能出现删除后起点不在原点的情况
    if (this.dag.isEmpty()) {
      return [node(0, 0)];
    }
    // 当前height下面的点没有元素
    if (!this.dag.getNodeByXy(0, this.getMaxY() + 1)) {
      const nodes = this.dag.map(v => {
        return node(v.x, v.y);
      });
      nodes.push(node(0, this.getMaxY() + 1));
      return nodes;
    }
    return this.dag.map(v => {
      return node(v.x, v.y);
    });

    // return _.range(0, this.getGrideX())
    //   .flatMap(x => {
    //     return _.range(0, this.getGrideY()).map(y => [x, y]);
    //   })
    //   .map(([x, y]) => node(x, y));
  }

  /**
   * 获得父节点分支，父节点必须只有一个
   * @param {array} fatherIds 父节点id
   * @param {number} branchIndex 分支id
   */
  __getNodeFatherBranch(fatherIds, branchIndex) {
    if (_.isEmpty(fatherIds) || fatherIds.length !== 1) return;
    const fatherNode = this.dag.getNodeByNodeId(fatherIds[0]);
    if (!fatherNode) {
      log.warn(`father node not exits, ${fatherIds[0]}`);
      return;
    }
    if (fatherNode.detail && !_.isEmpty(fatherNode.detail.branchList) && fatherNode.detail.branchList.length > branchIndex) {
      return fatherNode.detail.branchList[branchIndex];
    }
  }

  /**
   * 找到没有父分支的节点，并删除，导致这个结果的可能是节点编辑了branchList
   */
  __fixDeleteNoBranchRootNode() {
    log.debug('__deleteNoBranchRootNode');
    const willDeleteNode = this.dag
      .filter(v => v.branchIndex > 0)
      .filter(v => {
        const branch = this.__getNodeFatherBranch(v.fatherIds, v.branchIndex);
        return !branch;
      });
      // console.log('this.dag', this.dag);
      // console.log('willDeleteNode', willDeleteNode);
    // 父节点删除childrendIds，以至于不用再补0
    if (!_.isEmpty(willDeleteNode)) {
      this.dag.deleteBranch(willDeleteNode);
      return true;
    }
  }

  __replaceNode(flowNode, replacedNodeId) {
    log.debug('__replaceNode');
    const replacedNode = this.getNodeByNodeId(replacedNodeId);
    if (!replacedNode) {
      log.warn('替换节点未找到', replacedNodeId);
      return null;
    }
    const { flows } = this;
    const { x, y, branchIndex } = replacedNode;
    _.remove(flows, v => v.nodeId === replacedNode.nodeId);

    return this.__addNode(flowNode, x, y, branchIndex);
  }

  __fixAddSplitNodeBranchEnd() {
    log.debug('__addSplitNodeBranchEnd');
    const appendedEndNodes = this.dag
      .filter(v => v.joinType === 'SPLIT' && !_.isEmpty(v?.detail?.branchList))
      .flatMap(v => this.__fixSplitNodeBranchEndWithModel(v))
      .filter(v => v !== null);

    return appendedEndNodes;
  }

  __deleteJoinHelperEnd() {
    log.debug('__deleteJoinHelperEnd');
    const { flows } = this;
    const willDeleteNode = flows
      .filter(v => v.busiType === 'JoinHelperNode')
      .filter(v => !_.isEmpty(v.childrenIds))
      .map(v => this.getNodeByNodeId(v.childrenIds[0]))
      .filter(v => v);
    willDeleteNode.forEach(v => {
      this.__deleteNode(v);
    });
    return willDeleteNode;
  }

  __appendNode(model, appendingNode) {
    const appendedModel = new FlowNodeModelCanvas({ ...appendingNode });
    this.dag.appendData(appendedModel, model.nodeId);
    return appendedModel;
  }

  __appendEndNode(model) {
    log.debug('appendEndNode');
    const endModel = this.__getExitHelperNodeModel();
    if (!endModel) return;
    this.dag.appendData(endModel, model.nodeId);
    return endModel;
  }

  __fixAddEndNode() {
    const appendJoinHelperNodes = this.dag
      .filter(v => _.isEmpty(v.childrenIds.filter(nodeId => nodeId !== 0)) && v.busiType === 'JoinNode')
      .map(v => this.dag.appendData(new FlowNodeModelCanvas({ ...this.helperNodeMap.JoinHelperNode }), v.nodeId))
      .filter(v => v);
    const appendedEndNodes = this.dag
      // .filter(v => _.isEmpty(v.childrenIds.filter(nodeId => nodeId !== 0)) && v.joinType !== 'END' && v.busiType !== 'JoinHelperNode')
      .filter(v => _.isEmpty(v.childrenIds.filter(nodeId => nodeId !== 0)) && v.joinType !== 'END')
      .map(v => this.__appendEndNode(v))
      .filter(v => v);
    log.debug('__fixAddEndNode', appendedEndNodes);
    return appendedEndNodes.concat(appendJoinHelperNodes);
  }

  __getExitHelperNodeModel() {
    log.debug('__appendBranchEndNode');
    if (!this.helperNodeMap.ExitHelperNode) {
      log.warn('__appendEndNode 未配置退出节点');
      return null;
    }
    const endModel = new FlowNodeModelCanvas({ ...this.helperNodeMap.ExitHelperNode });
    return endModel;
  }

  /**
   *
   */
  __fixSplitNodeBranchEndWithModel(model) {
    const addedNodes = [];
    for (let i = 0; i < model.detail.branchList.length; i++) {
      // 如果有子节点，那么不处理
      if (model.childrenIds[i] > 0) continue;
      const endModel = this.__getExitHelperNodeModel();
      if (!endModel) return;
      this.dag.appendData(endModel, model.nodeId, i);
      addedNodes.push(endModel);
    }
    log.debug('__fixSplitNodeBranchEndWithModel', addedNodes);
    return addedNodes;
  }

  /**
   * 合并分支辅助节点很可能由于其父子关系解除后只剩下一个孤零的节点，这时要干掉 如 AppPush -> JoinHelper -> End或其他
   */
  __fixJoinHelperOnlyWithEndAndNoJoinNode() {
    const willDeleteNode = [];
    this.dag.forEach(v => {
      if (v.busiType === 'JoinHelperNode') {
        // 子类方法，不会abstract方法
        // if (v.fatherIds.length === 1 && v.childrenIds.length === 1) {
        //   willDeleteNode.push(v);
        // }
        const fatherNodes = _.map(v.fatherIds, id => this.dag.getNodeByNodeId(id));
        const hasJoinNode = _.some(fatherNodes, item => item.busiType === 'JoinNode');
        if (!hasJoinNode) {
          willDeleteNode.push(v);
        }
      }
    });
    willDeleteNode.forEach(v => {
      this.dag.deleteOneData(v.nodeId);
    });
    log.debug('__fixJoinHelperOnlyWithEndAndNoJoinNode', willDeleteNode);
    return willDeleteNode;
  }

  getNodePosByXY(x, y) {
    return {
      x: x * this.unitWidth + this.unitWidth / 2,
      y: y * this.unitHeight + this.unitHeight / 2
    };
  }

  __stepHalf(a1, a2, oneStep, forceLeft) {
    const step = forceLeft === undefined ? (a2 > a1 ? 1 : 0) : forceLeft;
    const a = parseInt(a1 / oneStep) * oneStep + step * oneStep;
    return a;
  }

  __connectPos(x1, y1, x2, y2, lines) {
    // log.debug('__connectPos');
    if (lines.length > 30) return;
    const { unitWidth, unitHeight } = this;
    if (x1 === x2 && y1 === y2) return;
    if (x1 % unitWidth !== 0 && y1 % unitHeight !== 0) {
      if (x1 !== x2) {
        const _x2 = this.__stepHalf(x1, x2, unitWidth);
        const _y2 = y1;
        lines.push({ x1, y1, x2: _x2, y2: _y2 });
        this.__connectPos(_x2, _y2, x2, y2, lines);
      } else {
        const _x2 = x1;
        const _y2 = this.__stepHalf(y1, y2, unitHeight);
        lines.push({ x1, y1, x2: _x2, y2: _y2 });
        this.__connectPos(_x2, _y2, x2, y2, lines);
      }
    } else if (x2 % unitWidth !== 0 && y2 % unitHeight !== 0) {
      this.__connectPos(x2, y2, x1, y1, lines);
    } else if (x1 % unitWidth !== 0) {
      const _x2 = this.__stepHalf(x1, x2, unitWidth);
      lines.push({ x1, y1, x2: _x2, y2: y1 });
      this.__connectPos(_x2, y1, x2, y2, lines);
    } else if (x2 % unitWidth !== 0) {
      this.__connectPos(x2, y2, x1, y1, lines);
    } else if (y1 % unitWidth !== 0) {
      const _y2 = this.__stepHalf(y1, y2, unitWidth);
      lines.push({ x1, y1, x2: x1, y2: _y2 });
      this.__connectPos(x1, _y2, x2, y2, lines);
    } else if (y2 % unitWidth !== 0) {
      this.__connectPos(x2, y2, x1, y1, lines);
    } else if (y1 !== y2) {
      lines.push({ x1, y1, x2: x1, y2 });
      this.__connectPos(x1, y2, x2, y2, lines);
    } else if (x1 !== x2) {
      lines.push({ x1, y1, x2, y2: y1 });
      this.__connectPos(x2, y1, x2, y2, lines);
    }
  }

  connectPos(x1, y1, x2, y2) {
    let lines = [];
    const headerPos = { x1, y1 };
    const tailPos = { x2, y2 };
    // 如果父子几点不在同一个x轴上，父永远先向右走一步，子节点永远先上左走一步
    if (x1 !== x2) {
      const { unitWidth } = this;
      const _x1 = this.__stepHalf(x1, x2, unitWidth, 1);
      const _y1 = y1;
      lines.push({ x1: Math.min(x1, _x1), y1, x2: Math.max(x1, _x1), y2: _y1 });
      x1 = _x1;
      y1 = _y1;

      const _x2 = this.__stepHalf(x2, x2, unitWidth, 0);
      const _y2 = y2;
      lines.push({ x1: Math.min(x2, _x2), y1: y2, x2: Math.max(x2, _x2), y2: _y2 });
      x2 = _x2;
      y2 = _y2;
    }
    this.__connectPos(x1, y1, x2, y2, lines);
    // 找到链接终点的线，调整线的方向为终点指向终点
    lines = _.map(lines, v => {
      if (v.x1 === tailPos.x2 && v.y1 === tailPos.y2) {
        return { x1: v.x2, y1: v.y2, x2: v.x1, y2: v.y1 };
      } else {
        return v;
      }
    });
    lines = _.map(lines, item => {
      return {
        ...item,
        connectHeader: item.x1 === headerPos.x1 && item.y1 === headerPos.y1,
        connectTail: item.x2 === tailPos.x2 && item.y2 === tailPos.y2,
        direction: this.getLineDirection(item.x1, item.y1, item.x2, item.y2)
      };
    });
    return lines;
  }

  getLineDirection(x1, y1, x2, y2) {
    if (x1 === x2) {
      return y1 < y2 ? 'down' : 'up';
    } else {
      return x1 < x2 ? 'right' : 'left';
    }
  }

  getConnectLine(x1, y1, x2, y2, lineFactory, fatherNodeId, childNodeId) {
    const pos1 = this.getNodePosByXY(x1, y1);
    const pos2 = this.getNodePosByXY(x2, y2);
    return lineFactory(this.connectPos(pos1.x, pos1.y, pos2.x, pos2.y), fatherNodeId, childNodeId);
  }

  getNodeConnectLines(lineFactory) {
    log.debug('getNodeConnectLines');
    const lines = this.dag
      .mapFatherChild((father, child) => {
        return this.getConnectLine(father.x, father.y, child.x, child.y, lineFactory, father.nodeId, child.nodeId);
      });
    return lines;
  }

  /**
   * 根据位置连接节点
   * @param {object} startPos {x, y}
   * @param {object} endPos {x, y}
   */
  connectPosNode(startPos, endPos) {
    log.debug('connectPosNode', startPos, endPos);
    const nodeStart = this.dag.getNodeByXy(startPos.x, startPos.y);
    if (!nodeStart) return;
    const nodeEnd = this.dag.getNodeByXy(endPos.x, endPos.y);
    if (!nodeEnd) return;
    log.debug('connectPosNode node', nodeStart.nodeId, nodeEnd.nodeId);
    // 判断是否子已经有了
    if (_.indexOf(nodeStart.childrenIds, nodeEnd.nodeId) >= 0 && _.indexOf(nodeEnd.fatherIds, nodeStart.nodeId) >= 0) return;
    // 设置branchIndex，branchIndex是父节点child节点数，如果父节点，没有相应的分支，则连接不成功
    const branchIndex = nodeStart.childrenIds.length;
    if (branchIndex > 0 && !this.__getNodeFatherBranch([nodeStart.nodeId], branchIndex)) {
      log.warn(`被连接的父节点，没有对应的分支, branchIndex=${branchIndex}  startNodeId=${nodeStart.nodeId} endNodeId=${nodeEnd.nodeId}`);
      return;
    }
    nodeStart.childrenIds.push(nodeEnd.nodeId);
    nodeEnd.fatherIds.push(nodeStart.nodeId);
    nodeEnd.branchIndex = branchIndex;
    log.debug(`成功连接节点 fatherNodeId=${nodeStart.nodeId} childNodeId=${nodeEnd.nodeId} branchIndex=${branchIndex}`);
    return {
      nodeStart,
      nodeEnd
    };
  }

  canDropBefore(draggingNode, insertBeforeNode) {
    if (!draggingNode || !insertBeforeNode) return false;
    return insertBeforeNode.canDropBefore(draggingNode);
  }

  /* ---------------------------------------- */

  __changeFlows() {
    const data = this.dag.getNodesData();
    this.#flows = data.map(v => v.toJson());
  }

  __fixBrokenSplitAndJoinHelperNode() {
    this.dag.forEach(v => {
      if (v.busiType === 'JoinHelperNode') {
        // 找到父节点中包含joinType为split的节点进行打断
        const fatherNodes = _.map(v.fatherIds, id => this.dag.getNodeByNodeId(id));
        const hasSplitNodes = _.some(fatherNodes, fNode => fNode.joinType === 'SPLIT');
        if (hasSplitNodes) {
          // 找到父结点中的合并分支节点进行打断
          const splitNodes = _.find(fatherNodes, fNode => fNode.busiType === 'JoinNode');
          splitNodes && this.breakChild(splitNodes);
        }
      }
    });
  }

  /**
   * 修复流程数据，删除没有父分支的节点，添加END节点（包括分支）
   * 如果修复了需要changeValue, 调用者需要重绘
   */
  __fixFlows() {
    // if (!this.autoFix) return;
    // log.debug('fixFLows');
    const deletedJoinHelperNode = !_.isEmpty(this.__fixJoinHelperOnlyWithEndAndNoJoinNode());
    const deletedNoBranchNode = this.__fixDeleteNoBranchRootNode();
    const appendedEndNode = !_.isEmpty(this.__fixAddEndNode());
    const addedSplitNodeEnd = !_.isEmpty(this.__fixAddSplitNodeBranchEnd());
    // // const deletedJoinHelperEnd = !_.isEmpty(this.__deleteJoinHelperEnd());
    const deletedNoNodeRow = this.dag.deleteNoNodeRow() > 0;
    const brokenSplitAndJoinHelperNode = !_.isEmpty(this.__fixBrokenSplitAndJoinHelperNode());
    const fixed = deletedJoinHelperNode | deletedNoBranchNode | appendedEndNode | addedSplitNodeEnd | deletedNoNodeRow | brokenSplitAndJoinHelperNode;
    // const fixed = deletedNoBranchNode | appendedEndNode | addedSplitNodeEnd | deletedNoNodeRow;
    return fixed;
  }

  /**
   * 删除节点
   * @param {FLowNodeCanvasModel} model 待删除节点
   */
  deleteNode(model) {
    log.debug('deleteNode', model);
    let deletedNode;
    if (model.canDeleteOnlyMe()) {
      deletedNode = this.dag.deleteOneData(model.nodeId);
    } else {
      deletedNode = this.dag.deleteData(model.nodeId);
    }
    if (deletedNode) this.__fixFlows();
    this.__changeFlows();
    return deletedNode;
  }

  /**
   * 添加节点
   * @param {number} x x坐标点
   * @param {number} y y坐标点
   * @param {object} flowNode 节点数据
   * @return 节点添加成功，需要重绘
   */
  addNode(x, y, flowNode) {
    log.debug('addNode', this.dag.getNodeByXy(x, y));
    if (this.dag.getNodeByXy(x, y)) {
      log.warn('添加节点位置已经存在节点');
      return null;
    }
    const model = new FlowNodeModelCanvas({ ...flowNode });
    this.dag.addData(model, x, y, 0);
    if (model.isEntry()) {
      this.__appendEndNode(model);
    }
    this.__fixFlows();
    this.__changeFlows();
    return model;
  }

  updateNode(model) {
    this.dag.updateData(model);
    this.__fixFlows();
    this.__changeFlows();
    return model;
  }

  /**
   * 在某节点前插入节点
   * @param {object} node 插入的节点model
   * @param {int} insertBeforeNodeId 在插入的节前id
   */
  insertNode(node, insertBeforeNodeId, fatherNodeId) {
    log.debug('__insertNode', node, insertBeforeNodeId, fatherNodeId);

    let model = null;
    // 被插入节点如果nodeId有值，表示被插入的节点来自流程图中，而不来自工具箱
    if (node.nodeId) {
      if (node.busiType === 'JoinHelperNode') {
        // 如果插入节点是合并分支的节点, 先插入合并分支节点，在插入合并分支辅助节点
        // 为什么要多插入一个合并分支节点???主要是branchIndex只有一个
        // model = new FlowNodeModelCanvas({ ...this.helperNodeMap.JoinNode });
        // this.dag.insertData(model, insertBeforeNodeId);

        // 拖动时已经合并过的分支的节点, 需要先删除结尾的end节点
        // if (node.getChild0() && node.getChildren().joinType === 'END') {
        //   this.dag.deleteData(this.getChild0().nodeId);
        // }

        model = new FlowNodeModelCanvas({ ...this.helperNodeMap.JoinHelperNode });
        const joiner = this.dag.insertData(model, insertBeforeNodeId, fatherNodeId);
        // 报留一下他的父id，因为删除后父id就掉了
        const nodeFatherIds = [...node.fatherIds];
        this.dag.deleteData(node.nodeId);
        nodeFatherIds.forEach(fatherId => {
          this.dag.addJoinNode(fatherId, joiner.nodeId);
        });
      } else if (node.joinType === 'FUNCTION') {
        // 如果拖动是一个一进一出的节点，则表示这个节点被拖过来复制一下
        const nodeCloned = _.cloneDeep(node.toJson());
        const flowModel = new FlowNodeModel({ ...nodeCloned });
        model = new FlowNodeModelCanvas({ ...flowModel.toJson(), displayName: nodeCloned.displayName, detail: nodeCloned.detail });
        this.dag.insertData(model, insertBeforeNodeId, fatherNodeId);
      }
    } else {
      model = new FlowNodeModelCanvas({ ...node });
      this.dag.insertData(model, insertBeforeNodeId, fatherNodeId);

      // 如果是合并分支组件需要添加helper
      if (model.busiType === 'JoinNode') {
        this.dag.replaceData(
          new FlowNodeModelCanvas({ ...this.helperNodeMap.JoinHelperNode }),
          model.childrenIds[0]
        );
      }
    }
    this.__fixFlows();
    this.__changeFlows();
    return model;
  }

  addNodeBranch(node) {
    if (!node || node.joinType !== 'SPLIT') return false;
    node.addTestBranch();
    this.__fixFlows();
    this.__changeFlows();
    return true;
  }

  breakChild(node) {
    const childNode = node.getChild0();
    if (node && childNode && childNode.busiType === 'JoinHelperNode') {
      log.debug('breakChild', node);
      // 首先找到childNode的所有父结点里除了joinNode外能打断的节点进行打断
      const fatherNodes = _.map(node.getChild0().fatherIds, fatherId => this.dag.getNodeByNodeId(fatherId));
      const breakableNodes = _.filter(fatherNodes, fatherNode => fatherNode.canBreakChild() && fatherNode.busiType !== 'JoinNode');
      if (!_.isEmpty(breakableNodes)) {
        _.forEach(breakableNodes, breakableNode => this.dag.breakChild(breakableNode.nodeId, childNode.nodeId));
      }
      this.dag.breakChild(node.nodeId, childNode.nodeId);
      this.__fixFlows();
      this.__changeFlows();
      return true;
    }
  }
}
