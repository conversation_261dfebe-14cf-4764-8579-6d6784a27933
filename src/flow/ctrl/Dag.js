// eslint-disable-next-line max-classes-per-file
import _ from 'lodash';
import FlowNodeModelCanvas from '../model/FlowNodeModelCanvas';
import Log from '../../utils/log';

const log = Log.getLogger('Dag');

class Node extends FlowNodeModelCanvas {
  constructor(data, dag) {
    super(data);
    _.forEach(data, (v, k) => {
      this[k] = v;
    });
    this.dag = dag;
  }

  getFathers() {
    return this.fatherIds.map(id => this.dag.getNodeByNodeId(id));
  }

  getChildren() {
    return this.childrenIds.map(id => this.dag.getNodeByNodeId(id));
  }

  getFather0() {
    const fathers = this.getFathers();
    if (fathers.length > 0) return fathers[0];
  }

  getChild0() {
    const children = this.getChildren();
    if (children.length > 0) return children[0];
  }

  getLeftNode() {
    const lefts = this.getFathers().filter(v => v.y === this.y && v.x < this.x);
    if (lefts.length > 0 && lefts[0]) {
      return lefts[0];
    }
  }

  /**
   * 对比的节点是否是当前节点的祖先，即直系father
   * @param {number} fahterNodeId 父节点
   * @param {number} childNodeId 子节点
   */
  isAncestor(fahterNodeId) {
    const childNodeId = this.nodeId;
    if (!fahterNodeId || !childNodeId) return false;
    const findFather = cid => {
      if (fahterNodeId === cid) return true;
      const childNode = this.dag.getNodeByNodeId(cid);
      const index = childNode.fatherIds.indexOf(fahterNodeId);
      if (index >= 0) {
        return true;
      }
      if (!childNode.fatherIds.length) return false;
      for (let i = 0; i < childNode.fatherIds.length; i++) {
        const ret = findFather(childNode.fatherIds[i]);
        if (ret) return true;
      }
      return false;
    };

    return findFather(childNodeId);
  }

  /**
   * nodeId是否是当前节点的直系子孙后代
   * @param {number} nodeId 比较id
   */
  isDescendants(nodeId) {
    return this.dag.getNodeByNodeId(nodeId).isAncestor(this.nodeId);
  }

  check() {
    super.check();
  }
}

export default class Dag {
  #nodeMap = {};

  #maxNodeId = 0;

  constructor(data) {
    const nodes = (data || []).map(v => new Node(v, this));

    this.#nodeMap = nodes.reduce((a, b) => {
      a[b.nodeId] = b;
      return a;
    }, {});

    this.#maxNodeId = this.getMaxNodeId();
    log.debug('constructor', this.#nodeMap);
  }

  check() {
    log.debug('check', this.#nodeMap);
    this.mapFatherChild((father, v) => {
      if (v.fatherIds.indexOf(father.nodeId) < 0) {
        log.warn('父子关系错误，fatherIds.indexOf(father.nodeId) < 0', v, father);
      }
      if (father.childrenIds.indexOf(v.nodeId) < 0) {
        log.warn('父子关系错误father.childrenIds.indexOf(v.nodeId) < 0', v, father);
      }
      v.fatherIds.forEach(nodeId => {
        if (!this.getNodeByNodeId(nodeId)) {
          log.warn('组件中包含不存在的父节点', v, nodeId);
        }
      });
      v.childrenIds.forEach(nodeId => {
        if (!this.getNodeByNodeId(nodeId)) {
          log.warn('组件中包含不存在的子节点', v, nodeId);
        }
      });
      v.check();
    });
  }

  /**
   * 获得开头数据
   */
  getHeaders() {
    return _.map(this.#nodeMap, v => v).filter(v => _.isEmpty(v.fatherIds));
  }

  getNodeXy() {
    const nodeXy = {};
    this.forEach(node => {
      if (!nodeXy[node.x]) {
        nodeXy[node.x] = {};
      }
      nodeXy[node.x][node.y] = node;
    });
    return nodeXy;
  }

  getNodeByXy(x, y) {
    const nodeXy = this.getNodeXy();
    if (!nodeXy || !nodeXy[x] || !nodeXy[x][y]) return null;
    return nodeXy[x][y];
  }

  getMaxNodeId() {
    let maxNodeId = this.map(v => v.nodeId).reduce((a, b) => Math.max(a, b), 0);
    if (isNaN(maxNodeId)) {
      maxNodeId = 10000;
    }
    return maxNodeId;
  }

  getNodesData() {
    return this.map(v => v);
  }

  generateNodeId() {
    this.#maxNodeId = this.#maxNodeId + 1;
    return this.#maxNodeId;
  }

  addData(data, x, y, branchIndex) {
    if (!data.nodeId) data.nodeId = this.generateNodeId();
    data.x = x;
    data.y = y;
    data.branchIndex = branchIndex;
    const node = new Node(data, this);
    this.#nodeMap[node.nodeId] = node;
    return node;
  }

  updateData(node) {
    this.#nodeMap[node.nodeId] = node;
    return node;
  }

  /**
   * 将父节点和目标点，链接在一起，处理多父的情况
   * @param {number} joinerId 链接的目标点
   * @param {number} fahterId 链接目标点的父节点
   */
  addJoinNode(fahterId, joinerId) {
    log.debug(`addJoinNode joinerId=${joinerId}, fahterId=${fahterId}`);
    const joiner = this.getNodeByNodeId(joinerId);
    const fahter = this.getNodeByNodeId(fahterId);
    joiner.fatherIds.push(fahterId);
    fahter.childrenIds = [joinerId];
  }

  __connectNode(fatherNodeId, childNodeId, branchIndex) {
    branchIndex = branchIndex || 0;
    const fatherNode = this.getNodeByNodeId(fatherNodeId);
    const childNode = this.getNodeByNodeId(childNodeId);
    fatherNode.childrenIds[branchIndex] = childNode.nodeId;
    childNode.branchIndex = branchIndex;
    if (childNode.fatherIds.indexOf(fatherNodeId) < 0) {
      childNode.fatherIds.push(fatherNodeId);
    }
  }

  __removeConnection(fatherNodeId, childNodeId) {
    if (fatherNodeId === 0 || childNodeId === 0) {
      // 很有可能调用移除站位的关系，直接返回就可以
      return;
    }
    const fatherNode = this.getNodeByNodeId(fatherNodeId);
    const childNode = this.getNodeByNodeId(childNodeId);
    const { branchIndex } = childNode;
    if (fatherNode.childrenIds.length > 1) {
      // 站位
      fatherNode.childrenIds[branchIndex] = 0;
    } else {
      _.remove(fatherNode.childrenIds, v => v === childNodeId);
    }
    _.remove(childNode.fatherIds, v => v === fatherNodeId);
  }

  insertData(data, insertBeforeNodeId, fatherNodeId) {
    log.debug('insertData', data, insertBeforeNodeId);
    const insertBeforeNode = this.getNodeByNodeId(insertBeforeNodeId);
    if (!insertBeforeNode) {
      log.warn('insertNode, 插入节点未找到', data, insertBeforeNodeId);
      return;
    }
    const { x, y, branchIndex, fatherIds } = insertBeforeNode;

    // if (_.isEmpty(fatherIds) && fatherIds.length !== 1) {
    //   log.warn('insertNode 父节点不唯一或为空', data, insertBeforeNode);
    //   return;
    // }
    if (_.isEmpty(fatherIds)) {
      log.warn('insertNode 父节点为空', data, insertBeforeNode);
      return;
    }
    let findIndex = 0;
    if (!_.isNil(fatherNodeId)) {
      findIndex = _.findIndex(fatherIds, v => v === fatherNodeId);
    }
    const fatherNode = this.getNodeByNodeId(fatherIds[findIndex]);
    if (!fatherNode) {
      log.warn('insertNode 父节点未找到', data, insertBeforeNode);
      return;
    }

    if (!fatherNode.childrenIds[branchIndex]) {
      log.warn('insertNode 父节点中未找到子节点为当前节点的引用', data, insertBeforeNode);
      return;
    }
    // 解除父子关系
    this.__removeConnection(fatherNode.nodeId, insertBeforeNodeId);

    this.addData(data, x, y, branchIndex);
    this.__connectNode(fatherNode.nodeId, data.nodeId, branchIndex);
    this.__connectNode(data.nodeId, insertBeforeNode.nodeId, 0);
    this.__moveNodeAndSubX(insertBeforeNode, 1, 'insert');
    return data;
  }

  replaceData(data, replaceNodeId) {
    const node = this.getNodeByNodeId(replaceNodeId);
    if (!node) {
      log.warn(`替换节点未找到 replaceNodeId=${replaceNodeId}`);
      return false;
    }
    this.__removeNode(node);

    const added = this.addData(data, node.x, node.y, node.branchIndex);

    node.fatherIds.forEach(f => {
      this.__connectNode(f, added.nodeId);
    });

    return added;
  }

  /**
   * 在某个节点后追加数据，包括SPLIT节点和普通节点
   * @param {FlowNodeCanvasModel} data 节点数据
   * @param {number} appendNodeId 在哪个节点后面追加
   * @param {number} branchIndex 追加在哪个branchIndex
   */
  appendData(data, appendNodeId, branchIndex) {
    branchIndex = branchIndex || 0;
    const node = this.getNodeByNodeId(appendNodeId);
    if (!node) {
      log.warn(`追加节点未找到 appendNodeId=${appendNodeId}`);
      return false;
    }
    let [x, y] = [0, 0];
    if (branchIndex === 0) {
      x = node.x + 1;
      y = node.y;
    } else {
      // 如果branchIndex不是0，说明添加的节点是分支节点，这时需要重新计算分支高度
      x = node.x + 1;
      y = node.y + this.getSubNodeHeight(node) + branchIndex;
      if (this.yHasNode(y)) {
        // 将下测和左侧分支全部+1
        // this.filter(v => v.x < x).forEach(v => v.y += 1);
        this.filter(v => v.y >= y).forEach(v => v.y += 1);
      }
    }
    const added = this.addData(data, x, y, branchIndex);
    this.__connectNode(node.nodeId, added.nodeId, branchIndex);
    return added;
  }

  /**
   * 根据节点id获得节点
   * @param {number} nodeId id
   */
  getNodeByNodeId(nodeId) {
    return this.#nodeMap[nodeId];
  }

  mapFatherChild(callback, node) {
    return this.__iteraterType(node, callback);
  }

  map(callback) {
    return _.map(this.#nodeMap, callback);
  }

  forEach(callback) {
    return _.map(this.#nodeMap, v => v).forEach(callback);
  }

  reduce(callback, initValue) {
    return _.map(this.#nodeMap, v => v).reduce(callback, initValue);
  }

  isEmpty() {
    return _.isEmpty(this.#nodeMap);
  }

  filter(callback) {
    return _.map(this.#nodeMap, v => v).filter(callback);
  }

  mapChildren(node, callback) {
    const results = [];
    const childrenNodes = node.childrenIds
      .map(v => this.getNodeByNodeId(v))
      .filter(v => v);
    childrenNodes
      .filter(v => v.fatherIds.includes(node.nodeId))
      .forEach(v => {
        results.push(callback(v, node));
        this.mapChildren(v, callback).forEach(r => results.push(r));
      });
    return results;
  }

  mapFather(node, callback) {
    const results = [];
    const nodes = node.fatherIds
      .map(v => this.getNodeByNodeId(v))
      .filter(v => v);
    nodes
      .filter(v => v.fatherIds.indexOf(node.nodeId) === 0)
      .forEach(v => {
        results.push(callback(v, node));
        this.mapFather(v, callback).forEach(r => results.push(r));
      });
    return results;
  }

  /**
   * 递归删除节点
   */
  deleteData(nodeId) {
    log.debug('deleteData', nodeId);
    const node = this.getNodeByNodeId(nodeId);
    const deletedNode = this.__deleteNodeChildrenSameRow(node);
    return deletedNode;
  }

  /**
   * 删除单一节点，而不递归删除，要接上删除后的节点
   */
  deleteOneData(nodeId) {
    log.debug('deleteOneData', nodeId);
    const node = this.getNodeByNodeId(nodeId);
    const fatherIds = [...node.fatherIds];
    const childrenIds = [...node.childrenIds];
    const branchIndex = node.branchIndex;

    fatherIds.forEach(fid => {
      this.__removeConnection(fid, node.nodeId);
    });
    childrenIds.forEach(cid => {
      this.__removeConnection(node.nodeId, cid);
    });
    this.__removeNode(node);

    fatherIds.forEach(fid => {
      childrenIds.forEach(cid => {
        this.__connectNode(fid, cid, branchIndex);
      });
    });

    childrenIds.forEach(cid => {
      this.__moveNodeAndSubX(this.getNodeByNodeId(cid), -1, 'delete');
    });
    return node;
  }

  // breakFather(child, father) {
  //   if (!father) father = child.getFather0();
  //   if (!father) return;
  //   const childInFatherIndex = father.childrenIds.indexOf(child.nodeId);
  //   if (childInFatherIndex < 0) {
  //     log.warn('无法解除无父子关系节点', father, child);
  //     return;
  //   }
  //   if (childInFatherIndex !== father.childrenIds.length) {
  //     // 站位
  //     father.childrenIds[childInFatherIndex] = 0;
  //   } else {
  //     _.remove(father.childrenIds, id => id === child.nodeId);
  //   }
  //   _.remove(child.fatherIds, id => id === father.nodeId);
  // }

  __iterater(node, callback) {
    let results = [];
    const childrenNodes = node.childrenIds
      .map(v => this.getNodeByNodeId(v))
      .filter(v => v);
    childrenNodes.forEach(v => {
      // log.debug('__iterater', node.nodeId, v.nodeId);
      results.push(callback(node, v));
    });
    childrenNodes
      .filter(v => v.fatherIds.indexOf(node.nodeId) === v.fatherIds.length - 1)
      .forEach(v => {
        results = results.concat(this.__iterater(v, callback));
      });
    return results;
  }

  __iteraterType(node, callback) {
    const nodes = node ? [node] : this.getHeaders();
    return nodes.flatMap(v => this.__iterater(v, callback));
  }

  /**
   * 删除一个节点
   * @param {Node} node 被删除节点
   */
  __removeNode(node) {
    delete this.#nodeMap[node.nodeId];
  }

  // __deleteNodeAndNoFatherChildren(node) {
  //   log.debug('__deleteNodeAndNoFatherChildren');
  //   const deletedNodes = this.mapChildren(node, (v, father) => {
  //     // 删除的节点可能有多个父，打断那个链接
  //     v.fatherIds
  //       .filter(fid => fid !== father.nodeId)
  //       .forEach(fid => {
  //         this.__removeConnection(fid, v.nodeId);
  //       });
  //     this.__removeNode(v);
  //     return v;
  //   });
  //   node.fatherIds.forEach(fid => {
  //     this.__removeConnection(fid, node.nodeId);
  //   });
  //   this.__removeNode(node);
  //   deletedNodes.push(node);
  //   return deletedNodes;
  // }

  __deleteNodeChildrenSameRow(node) {
    log.debug('__deleteNodeChildrenSameRow');
    if (!this.getNodeByNodeId(node.nodeId)) {
      log.warn('删除节点不存在', node);
      return;
    }
    // 将删除同行的且是node的子节点的节点
    // const willDeletedNodes = this.mapChildren(node, v => v)
    //   .filter(v => v.y === node.y || v.fatherIds.length === 1);
    // willDeletedNodes.push(node);
    // // 解除父子关系
    // willDeletedNodes.forEach(v => {
    //   [...v.fatherIds].forEach(fid => {
    //     this.__removeConnection(fid, v.nodeId);
    //   });
    //   [...v.childrenIds].forEach(cid => {
    //     this.__removeConnection(v.nodeId, cid);
    //   });
    // });
    // willDeletedNodes.forEach(v => {
    //   this.__removeNode(v);
    // });
    // 在这里再判断一下，子节点如果存在无父的情况，还要继续删除这种节点, 造成这种情况的原因是多分支合并于一点，最后会留下几个合并分支节点
    // 打断当前节点和父的关系

    node.fatherIds.forEach(fid => {
      this.__removeConnection(fid, node.nodeId);
    });
    const willDeletedNodes = this.deepMapFatherChild((father, child) => {
      // console.log(father.x, father.y, father.fatherIds, father.childrenIds, child.x, child.y);
      if (child.y === node.y || child.fatherIds.length === 0 || father.fatherIds.length === 0) {
        // console.log('__removeConnection', father.x, father.y, child.x, child.y);
        this.__removeConnection(father.nodeId, child.nodeId);
      }
    }, current => {
      // console.log('current', current.x, current.y);
      if (current.fatherIds.filter(id => id !== 0).length === 0 && current.childrenIds.filter(id => id !== 0).length === 0) {
        return current;
      }
      // 同行的未删掉的继续删除
      if (current.y === node.y) {
        this.__deleteNodeChildrenSameRow(current);
      }
    }, node).filter(v => v);
    willDeletedNodes.push(node);
    willDeletedNodes.forEach(v => this.__removeNode(v));
    return willDeletedNodes;
  }

  /**
   * 当遇到合并分支的节点
   * @param {function} callback 回调
   * @param {DagNode} node 节点
   */
  deepMapFatherChild(fatherChildCallback, nodeCallback, node) {
    const nodes = node ? [node] : this.getHeaders();
    const iter = father => {
      const results = [];
      const childrenIds = [...father.childrenIds];
      father.childrenIds
        .map(v => this.getNodeByNodeId(v))
        .filter(v => v)
        .forEach(child => {
          fatherChildCallback(father, child);
        });
      results.push(nodeCallback(father));
      // results.push(callback(father));
      childrenIds
        .map(v => this.getNodeByNodeId(v))
        .filter(v => v)
        // .filter(v => v.fatherIds.indexOf(father.nodeId) === 0)
        .flatMap(v => iter(v))
        .forEach(v => {
          results.push(v);
        });
      return results;
    };
    return nodes.flatMap(v => iter(v, fatherChildCallback, nodeCallback));
  }

  __moveNodeAndSubX(node, x, type) {
    log.debug('__moveNodeAndSubX');
    node.x += x;
    this.mapChildren(node, v => {
      if (type === 'delete') {
        // 当左侧有节点的时候不能移动
        const movedX = v.x + x;
        if (this.getNodeByXy(movedX, v.y) === null) {
          v.x = movedX;
        }
      } else {
        v.x += x;
      }
    });
  }

  /**
   * 获得子节点的总高度，逻辑是每个分支必须占一行
   * @param {FlowNodeModelCanvas} node 节点
   */
  getSubNodeHeight(node) {
    const getNodeHeight = n => {
      let height = 0;
      if (n?.detail?.branchList?.length) {
        height += n?.detail?.branchList?.length - 1;
      }
      n.childrenIds.forEach(v => {
        const _n = this.getNodeByNodeId(v);
        if (_n) {
          height += getNodeHeight(_n);
        }
      });
      return height;
    };
    const height = node.childrenIds
      .map(id => this.getNodeByNodeId(id))
      .filter(v => v)
      .map(v => getNodeHeight(v))
      .reduce((a, b) => a + b, 0);
    log.debug('node的节点高度是', height, node);
    return height;
  }

  yHasNode(y, excludeNodeIds) {
    if (!excludeNodeIds) excludeNodeIds = [];
    return !_.isEmpty(this.filter(v => v.y === y).filter(v => _.isEmpty(excludeNodeIds.filter(id => v.nodeId === id))));
  }

  /**
   * 删除整行没有节点的行
   */
  deleteNoNodeRow() {
    // 删除导致整行没有节点的行号，即y
    const maxY = this.map(v => v.y).reduce((a, b) => Math.max(a, b), 0);
    const allY = _.range(0, maxY + 1);
    const noNodeRow = _.reverse(allY.filter(v => !this.yHasNode(v)));
    // 自下而上删除行, 即将其下面的全部节点上提一行
    noNodeRow.forEach(y => {
      log.debug(`向上提升一行y=${y}`);
      this.filter(v => v.y > y).forEach(v => (v.y -= 1));
    });
    return noNodeRow.length;
  }

  /**
   * 删除节点的分支
   * @param {Node} nodeId 父节点
   * @param {number} branchIndex 父节点的分支
   */
  deleteBranch(willDeleteNode) {
    if (!willDeleteNode[0]?.fatherIds[0]) {
      log.warn('deleteBranch nodeId not exists', willDeleteNode[0]?.fatherIds[0]);
      return;
    }
    const fatherNode = this.getNodeByNodeId(willDeleteNode[0].fatherIds[0]);
    _.forEach(willDeleteNode, v => {
      const childNode = this.getNodeByNodeId(v.nodeId);
      _.remove(fatherNode.childrenIds, subV => subV === childNode.nodeId);
      _.remove(childNode.fatherIds, subV => subV === fatherNode.nodeId);
      this.__deleteNodeChildrenSameRow(this.getNodeByNodeId(childNode.nodeId));
    });

    return true;
  }

  breakChild(fatherNodeId, childNodeId) {
    return this.__removeConnection(fatherNodeId, childNodeId);
  }
}
