import React, { useState, useEffect } from 'react';
import { DownOutlined } from '@ant-design/icons';
import { Dropdown, Menu, Input } from 'antd';
import FilterConfig from './FilterConfig';
import { t } from '../utils/translation';

const { DATE_TYPE_MAP, DATE_TYPE_NOLATEST_MAP } = FilterConfig;
// 操作符map
// const OPERATOR_MAP = operatorList.reduce((map, obj) => {
//   map[obj.operator] = obj;
//   return map;
// }, {});

export default function FilterdateType({ value, onChange }) {
  const { dateType, tagInfo } = value;
  const [searchText, setSearchText] = useState(
    DATE_TYPE_MAP[dateType] || 'RELATIVE'
  );
  const [menuVisible, setMenuVisible] = useState(false);

  useEffect(() => {
    if (menuVisible) {
      setSearchText('');
    } else if (DATE_TYPE_MAP[dateType]) {
      setSearchText(DATE_TYPE_MAP[dateType]);
    }
  }, [dateType, menuVisible]);

  useEffect(() => {
    setSearchText(DATE_TYPE_MAP[dateType] || '');
  }, [dateType]);

  const operatorMenu = () => {
    return Object.entries(
      tagInfo?.busiDate ? DATE_TYPE_MAP : DATE_TYPE_NOLATEST_MAP
    )
      .filter(v => !searchText || v[1].indexOf(searchText) >= 0)
      .map((p, i) => {
        return (
          <Menu.Item key={i} onClick={() => onSelect(p[0])}>
            {p[1]}
          </Menu.Item>
        );
      });
  };

  const onSelect = type => {
    const resetTimes = type !== value.dateType;
    value.changeDateType(type, resetTimes);
    value.changeRule('checkUserTag', false);
    onChange(value);
    setSearchText(DATE_TYPE_MAP[value.dateType]);
  };

  // const getTypeOperators = () => {
  //   // return typeOperator.LABEL;
  //   return typeOperator[value.fieldType || 'STRING'];
  // };

  const menu = () => {
    return (
      <Menu
        forceSubMenuRender
        style={{
          maxHeight: 400,
          overflowY: 'auto',
          maxWidth: 500,
          overflowX: 'auto'
        }}
      >
        {value?.id && operatorMenu()}
      </Menu>
    );
  };

  const onMenuVisible = v => {
    if (v) {
      setSearchText('');
    }
    setMenuVisible(v);
  };

  return (
    <Dropdown
      getPopupContainer={triggerNode => triggerNode.parentNode}
      overlay={menu}
      trigger={['click']}
      onOpenChange={onMenuVisible}
    >
      <div className="clickWrapper">
        <Input
          className="ant-dropdown-link"
          placeholder={t('cpnt-hE2GSadmjh')}
          // suffix={<DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />}
          onChange={e => setSearchText(e.target.value)}
          onFocus={event => event.target.select()}
          value={searchText}
        />
        <DownOutlined style={{ color: 'rgba(0,0,0,.45)', fontSize: 12 }} />
      </div>
    </Dropdown>
  );
}
