import { FilterValue, DataProvider } from '../filter/types';

/**
 * 活动信息接口
 */
export interface CampaignInfo {
  [key: string]: any;
}

/**
 * Label组件Props接口
 */
export interface LabelProps {
  /**
   * @description 标签过滤器的值
   */
  value?: FilterValue;

  /**
   * @description 数据提供器
   */
  dataProvider: DataProvider;

  /**
   * @description 值改变时的回调函数
   */
  onChange?: (value: FilterValue, innerValue: FilterValue) => void;

  /**
   * @description 组件模式
   * @default 'edit'
   */
  mode?: 'edit' | 'detail';

  /**
   * @description 是否显示初始行
   * @default true
   */
  showInitLine?: boolean;

  /**
   * @description 是否选中状态
   * @default false
   */
  checked?: boolean;

  /**
   * @description 活动信息
   * @default {}
   */
  campaignInfo?: CampaignInfo;

  /**
   * @description 是否为用户分群模式
   */
  isUserGroup?: boolean;

  /**
   * @description 是否为活动V2版本
   */
  isCampaignV2?: boolean;
}

/**
 * Label组件Ref方法接口
 */
export interface LabelRef {
  /**
   * @description 校验标签过滤器是否有效
   * @param flag 是否显示验证错误
   * @returns 是否有效
   */
  isValid: (flag?: boolean) => boolean;

  /**
   * @description 获取过滤条件总数
   * @returns 总数
   */
  getFilterCount: () => number;
}
