#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}Git Tag 创建和推送脚本${NC}"
echo "=================================="

# 检查是否在git仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo -e "${RED}错误: 当前目录不是一个Git仓库${NC}"
    exit 1
fi

# 显示当前分支和最近的提交
echo -e "${YELLOW}当前分支:${NC} $(git branch --show-current)"
echo -e "${YELLOW}最近的提交:${NC}"
git log --oneline -3

echo ""

# 显示现有的tags
echo -e "${YELLOW}现有的tags:${NC}"
git tag -l | tail -10 | sort -V

echo ""

# 提示输入tag名称
read -p "请输入要创建的tag名称 (例如: v1.5.6): " tag_name

# 验证输入
if [ -z "$tag_name" ]; then
    echo -e "${RED}错误: tag名称不能为空${NC}"
    exit 1
fi

# 检查tag是否已存在
if git tag -l | grep -q "^$tag_name$"; then
    echo -e "${RED}错误: tag '$tag_name' 已经存在${NC}"
    exit 1
fi

# 询问是否添加注释
read -p "是否要添加tag注释? (y/n): " add_message

if [[ $add_message =~ ^[Yy]$ ]]; then
    read -p "请输入tag注释: " tag_message
    echo -e "${YELLOW}创建带注释的tag: $tag_name${NC}"
    git tag -a "$tag_name" -m "$tag_message"
else
    echo -e "${YELLOW}创建轻量级tag: $tag_name${NC}"
    git tag "$tag_name"
fi

# 确认创建成功
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✓ Tag '$tag_name' 创建成功${NC}"
else
    echo -e "${RED}✗ Tag 创建失败${NC}"
    exit 1
fi

# 询问是否推送到远程
read -p "是否要推送tag到远程仓库? (y/n): " push_remote

if [[ $push_remote =~ ^[Yy]$ ]]; then
    echo -e "${YELLOW}推送tag到远程仓库...${NC}"
    git push origin "$tag_name"
    
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}✓ Tag '$tag_name' 已成功推送到远程仓库${NC}"
    else
        echo -e "${RED}✗ Tag 推送失败${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}Tag '$tag_name' 已创建但未推送到远程${NC}"
fi

echo -e "${GREEN}完成!${NC}" 